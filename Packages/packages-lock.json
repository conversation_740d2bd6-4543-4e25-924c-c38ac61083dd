{"dependencies": {"com.coffee.softmask-for-ugui": {"version": "https://github.com/mob-sakai/SoftMaskForUGUI.git?path=Packages/src", "depth": 0, "source": "git", "dependencies": {"com.unity.ugui": "1.0.0"}, "hash": "b63f3c8233e9a58a480f4e2d353c83c08a584a88"}, "com.coffee.ui-effect": {"version": "https://github.com/mob-sakai/UIEffect.git?path=Packages/src", "depth": 0, "source": "git", "dependencies": {"com.unity.ugui": "1.0.0"}, "hash": "8a0e7d6b26fdd6eae7ade609495a11fa12cb543a"}, "com.coffee.ui-particle": {"version": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "depth": 0, "source": "git", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.particlesystem": "1.0.0"}, "hash": "4b98abd7469b10d55c9c623830cb6e1518f72731"}, "com.cysharp.unitask": {"version": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "depth": 0, "source": "git", "dependencies": {}, "hash": "06067cd4c83e372310d6f79d1c625f6870ff7a2a"}, "com.gameworkstore.googleprotobufunity": {"version": "https://github.com/GameWorkstore/google-protobuf-unity.git#3.15.2012", "depth": 0, "source": "git", "dependencies": {}, "hash": "655ece78bda22e0aa28593d8dddccbca6de719bb"}, "com.google.external-dependency-manager": {"version": "1.2.183", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://package.openupm.com"}, "com.google.firebase.analytics": {"version": "file:../GooglePackages/com.google.firebase.analytics-12.0.0.tgz", "depth": 0, "source": "local-tarball", "dependencies": {"com.google.firebase.app": "12.0.0"}}, "com.google.firebase.app": {"version": "file:../GooglePackages/com.google.firebase.app-12.0.0.tgz", "depth": 0, "source": "local-tarball", "dependencies": {"com.google.external-dependency-manager": "1.2.177"}}, "com.google.firebase.crashlytics": {"version": "file:../GooglePackages/com.google.firebase.crashlytics-12.0.0.tgz", "depth": 0, "source": "local-tarball", "dependencies": {"com.google.firebase.app": "12.0.0"}}, "com.google.firebase.remote-config": {"version": "file:../GooglePackages/com.google.firebase.remote-config-12.0.0.tgz", "depth": 0, "source": "local-tarball", "dependencies": {"com.google.firebase.app": "12.0.0"}}, "com.kyrylokuzyk.primetween": {"version": "file:../Assets/Plugins/PrimeTween/internal/com.kyrylokuzyk.primetween.tgz", "depth": 0, "source": "local-tarball", "dependencies": {}}, "com.unity.2d.animation": {"version": "10.1.4", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "9.0.7", "com.unity.2d.sprite": "1.0.0", "com.unity.collections": "1.2.4", "com.unity.modules.animation": "1.0.0", "com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.aseprite": {"version": "1.1.7", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "6.0.6", "com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.2.6", "com.unity.modules.animation": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.common": {"version": "9.0.7", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.8.4", "com.unity.2d.sprite": "1.0.0", "com.unity.mathematics": "1.1.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.uielements": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.pixel-perfect": {"version": "5.0.3", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.2d.psdimporter": {"version": "9.0.3", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "9.0.4", "com.unity.2d.sprite": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.sprite": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.2d.spriteshape": {"version": "10.0.7", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.common": "9.0.7", "com.unity.mathematics": "1.1.0", "com.unity.modules.physics2d": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.2d.tilemap": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {"com.unity.modules.tilemap": "1.0.0", "com.unity.modules.uielements": "1.0.0"}}, "com.unity.2d.tilemap.extras": {"version": "4.1.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.2d.tilemap": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.addressables": {"version": "2.2.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.profiling.core": "1.0.2", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.scriptablebuildpipeline": "2.1.4", "com.unity.modules.unitywebrequestassetbundle": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.burst": {"version": "1.8.18", "depth": 2, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.collab-proxy": {"version": "2.7.1", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.collections": {"version": "2.5.1", "depth": 2, "source": "registry", "dependencies": {"com.unity.burst": "1.8.17", "com.unity.test-framework": "1.4.5", "com.unity.nuget.mono-cecil": "1.11.4", "com.unity.test-framework.performance": "3.0.3"}, "url": "https://packages.unity.com"}, "com.unity.editorcoroutines": {"version": "1.0.0", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.ext.nunit": {"version": "2.0.5", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.feature.2d": {"version": "2.0.1", "depth": 0, "source": "builtin", "dependencies": {"com.unity.2d.animation": "10.1.4", "com.unity.2d.pixel-perfect": "5.0.3", "com.unity.2d.psdimporter": "9.0.3", "com.unity.2d.sprite": "1.0.0", "com.unity.2d.spriteshape": "10.0.7", "com.unity.2d.tilemap": "1.0.0", "com.unity.2d.tilemap.extras": "4.1.0", "com.unity.2d.aseprite": "1.1.7"}}, "com.unity.ide.rider": {"version": "3.0.36", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "1.0.6"}, "url": "https://packages.unity.com"}, "com.unity.ide.visualstudio": {"version": "2.0.22", "depth": 0, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.9"}, "url": "https://packages.unity.com"}, "com.unity.mathematics": {"version": "1.3.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.mobile.android-logcat": {"version": "1.4.5", "depth": 0, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.mono-cecil": {"version": "1.11.4", "depth": 3, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.nuget.newtonsoft-json": {"version": "3.2.1", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.profiling.core": {"version": "1.0.2", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.purchasing": {"version": "4.12.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.services.core": "1.12.5", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.render-pipelines.core": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.burst": "1.8.14", "com.unity.mathematics": "1.3.2", "com.unity.ugui": "2.0.0", "com.unity.collections": "2.4.3", "com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.rendering.light-transport": "1.0.1"}}, "com.unity.render-pipelines.universal": {"version": "file:com.unity.render-pipelines.universal", "depth": 0, "source": "embedded", "dependencies": {"com.unity.render-pipelines.core": "17.0.3", "com.unity.shadergraph": "17.0.3", "com.unity.render-pipelines.universal-config": "17.0.3"}}, "com.unity.render-pipelines.universal-config": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3"}}, "com.unity.rendering.light-transport": {"version": "1.0.1", "depth": 2, "source": "builtin", "dependencies": {"com.unity.collections": "2.2.0", "com.unity.mathematics": "1.2.4", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.scriptablebuildpipeline": {"version": "2.1.4", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.searcher": {"version": "4.9.2", "depth": 2, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.services.core": {"version": "1.14.0", "depth": 1, "source": "registry", "dependencies": {"com.unity.modules.androidjni": "1.0.0", "com.unity.nuget.newtonsoft-json": "3.2.1", "com.unity.modules.unitywebrequest": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.settings-manager": {"version": "2.0.1", "depth": 1, "source": "registry", "dependencies": {}, "url": "https://packages.unity.com"}, "com.unity.shadergraph": {"version": "17.0.3", "depth": 1, "source": "builtin", "dependencies": {"com.unity.render-pipelines.core": "17.0.3", "com.unity.searcher": "4.9.2"}}, "com.unity.splines": {"version": "2.7.2", "depth": 0, "source": "registry", "dependencies": {"com.unity.mathematics": "1.2.1", "com.unity.settings-manager": "1.0.3"}, "url": "https://packages.unity.com"}, "com.unity.test-framework": {"version": "1.4.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.ext.nunit": "2.0.3", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.test-framework.performance": {"version": "3.0.3", "depth": 3, "source": "registry", "dependencies": {"com.unity.test-framework": "1.1.31", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.ugui": {"version": "2.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0"}}, "com.unity.visualscripting": {"version": "1.9.5", "depth": 0, "source": "registry", "dependencies": {"com.unity.ugui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}, "url": "https://packages.unity.com"}, "com.unity.modules.accessibility": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.ai": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.androidjni": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.animation": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.assetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.audio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.director": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.audio": "1.0.0", "com.unity.modules.animation": "1.0.0"}}, "com.unity.modules.hierarchycore": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.imageconversion": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.imgui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.jsonserialize": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.particlesystem": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.physics2d": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.screencapture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.terrain": {"version": "1.0.0", "depth": 1, "source": "builtin", "dependencies": {}}, "com.unity.modules.terrainphysics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics": "1.0.0", "com.unity.modules.terrain": "1.0.0"}}, "com.unity.modules.tilemap": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.physics2d": "1.0.0"}}, "com.unity.modules.ui": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.uielements": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.ui": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.hierarchycore": "1.0.0"}}, "com.unity.modules.umbra": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unityanalytics": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0"}}, "com.unity.modules.unitywebrequest": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {}}, "com.unity.modules.unitywebrequestassetbundle": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0"}}, "com.unity.modules.unitywebrequestaudio": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.audio": "1.0.0"}}, "com.unity.modules.unitywebrequesttexture": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}, "com.unity.modules.unitywebrequestwww": {"version": "1.0.0", "depth": 0, "source": "builtin", "dependencies": {"com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.imageconversion": "1.0.0"}}}}