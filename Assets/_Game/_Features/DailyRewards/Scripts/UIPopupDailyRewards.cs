using UnityEngine;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine.UI;
using System.Collections.Generic;
using OnePuz.Data;
using System;
using System.Linq;
using OnePuz.Attributes;
using OnePuz.Extensions;
using OnePuz.DailyReward.Definitions;
using TMPro;
using OnePuz.UI;
using PrimeTween;

namespace OnePuz.DailyReward.UI
{
    public class UIPopupDailyReward : UIBasePanel
    {
        private class UIReward
        {
            private Image _iconImage;
            private TMP_Text _quantityText;

            public UIReward(Transform holder, RewardData reward)
            {
                _iconImage = holder.FindDeep<Image>("Icon");
                _quantityText = holder.FindDeep<TMP_Text>("TextQuantity");

                var definition = Core.GetRewardDefinition(reward);
                _iconImage.sprite = definition.icon;
                _quantityText.text = reward.RewardType == RewardType.Currency ? $"{reward.Quantity}" : $"X{reward.Quantity}";
            }
        }

        public class UIItem
        {
            private readonly Animator _animator;

            private readonly GameObject _panelNormal;
            private readonly GameObject _panelActive;

            private readonly Animator _checkmarkAnimator;

            private TMP_Text _dayText;

            private Transform _rewardContainer;
            private Transform _rewardItemPrefab;

            private readonly GameObject _highlightVFX;

            private readonly int _day;

            private static readonly int ANIMATION_CHECKMARK_FADE_IN = Animator.StringToHash("PopupDailyReward_Item_Checkmark_FadeIn");
            private static readonly int ANIMATION_FADE_IN = Animator.StringToHash("PopupDailyReward_Item_FadeIn");

            public UIItem(Transform holder, int day, List<RewardData> rewards)
            {
                _day = day - 1;

                _animator = holder.GetComponent<Animator>();

                var panelNormal = holder.FindDeep("PanelNormal");
                _panelNormal = panelNormal.gameObject;
                _panelActive = holder.FindDeep("PanelActive").gameObject;

                _checkmarkAnimator = holder.FindDeep<Animator>("PanelCheck");

                _dayText = panelNormal.FindDeep<TMP_Text>("TextDay");

                _rewardContainer = holder.FindDeep("Rewards");

                _dayText = panelNormal.Find("TextDay").GetComponent<TMP_Text>();
                _dayText.text = $"DAY {day}";

                _rewardContainer = holder.Find("Rewards");
                _rewardItemPrefab = _rewardContainer.GetChild(0);

                _highlightVFX = holder.FindDeep("HighlightVFX").gameObject;

                for (var i = 0; i < rewards.Count; i++)
                {
                    var itemTransform = i == 0
                        ? _rewardContainer.GetChild(0)
                        : Instantiate(_rewardItemPrefab, _rewardContainer);
                    var reward = new UIReward(itemTransform, rewards[i]);
                }
            }

            public UIItem UpdateState(int currentDay, bool claimedCurrentDay)
            {
                _panelNormal.SetActive(_day != currentDay);
                _panelActive.SetActive(_day == currentDay);
                _highlightVFX.SetActive(_day == currentDay && !claimedCurrentDay);
                var alreadyClaimed = _day < currentDay || (_day == currentDay && claimedCurrentDay);
                _checkmarkAnimator.SetState(ANIMATION_CHECKMARK_FADE_IN, isReversed: alreadyClaimed);
                return this;
            }

            public UIItem SetBeforeFadeInState()
            {
                _animator.SetState(ANIMATION_FADE_IN);
                return this;
            }

            public Tween PlayCheckAnimation()
            {
                return _checkmarkAnimator.PlayManual(ANIMATION_CHECKMARK_FADE_IN);
            }

            public Tween PlayFadeIn()
            {
                return _animator.PlayManual(ANIMATION_FADE_IN);
            }

            public Tween PlayFadeOut()
            {
                return _animator.PlayReverse(ANIMATION_FADE_IN);
            }
        }

        private static readonly int ANIMATION_FADE_IN = Animator.StringToHash("PopupDailyReward_FadeIn");
        private static readonly int ANIMATION_FADE_OUT = Animator.StringToHash("PopupDailyReward_FadeOut");

        [Header("Popup Animation")]
        [SerializeField, PreAssigned()]
        private Animator _animator;

        [Header("Elements")]
        [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
        private Button _closeButton;

        [SerializeField, PreAssigned("Container/Popup/ButtonRewardX2")]
        private Button _rewardX2Button;

        [SerializeField, PreAssigned("Container/Popup/ButtonGet")]
        private Button _getButton;

        [SerializeField, PreAssigned("Container/Popup/Items")]
        private Transform _itemsContainer;

        private readonly List<UIItem> _items = new();

        private DailyRewardDefinition _definition;

        public override void Init(string id)
        {
            base.Init(id);

            _definition = Core.Definition.DailyReward;

            _closeButton.onClick.AddListener(OnCloseButtonClicked);
            _rewardX2Button.onClick.AddListener(OnRewardX2ButtonClicked);
            _getButton.onClick.AddListener(OnGetButtonClicked);

            SetupItems();
        }

        private void SetupItems()
        {
            for (var i = 0; i < _definition.data.Count; i++)
            {
                var datum = _definition.data[i];
                var item = new UIItem(_itemsContainer.Find($"Item_{i}"), datum.day, datum.rewards);
                _items.Add(item);
            }
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            var currentDay = DataShortcut.DailyReward.CurrentDay;
            var isClaimed = DataShortcut.DailyReward.IsClaimed;

            for (var i = 0; i < _items.Count; i++)
            {
                _items[i]
                    .UpdateState(currentDay, isClaimed)
                    .SetBeforeFadeInState();
            }

            _getButton.gameObject.SetActive(!isClaimed);
            _rewardX2Button.gameObject.SetActive(!isClaimed);
            _closeButton.gameObject.SetActive(isClaimed);
        }

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();

            _closeButton.onClick.AddListener(OnCloseButtonClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _closeButton.onClick.RemoveListener(OnCloseButtonClicked);
        }

        private void OnCloseButtonClicked()
        {
            Close();
        }

        private void OnGetButtonClicked()
        {
            ClaimAsync().Forget();
        }

        private void OnRewardX2ButtonClicked()
        {
            if (Core.Ads.IsRewardedVideoLoaded())
            {
                Core.Ads.SubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);
                Core.Ads.ShowRewardedVideo();
                
                Core.Analytic.LogEvent("rv_daily_reward_x2", new Firebase.Analytics.Parameter[]
                {
                    new Firebase.Analytics.Parameter("level", DataShortcut.Level.Current + 1)
                });
            }
            else
            {
                Core.Ads.RequestRewardedVideo();

                UIShortcut.ShowNoAdsAvailable();
            }
        }

        private void HandleOnWatchVideoReward(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);

            if (!succeed) return;

            ClaimAsync(2).Forget();
        }

        private async UniTask ClaimAsync(int factor = 1)
        {
            pRaycaster.enabled = false;

            var currentDay = DataShortcut.DailyReward.CurrentDay;
            var rewards = _definition.data[currentDay].rewards;
            var calculatedRewards = rewards.Select(reward => new RewardData
            {
                RewardType = reward.RewardType,
                CurrencyType = reward.CurrencyType,
                BoosterType = reward.BoosterType,
                Quantity = reward.Quantity * factor
            }).ToList();

            var item = _items[currentDay];
            await item.PlayCheckAnimation().ToUniTask(cancellationToken: _cancellationToken);

            Close();

            UIShortcut.ShowCongratulationPanel<UIPanelHome>(calculatedRewards);

            DataShortcut.DailyReward.Claim();
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            _animator.PlayManual(ANIMATION_FADE_IN).Forget(cancellationToken);

            await UniTask.Delay(TimeSpan.FromSeconds(0.2f), cancellationToken: cancellationToken);

            for (var i = 0; i < _items.Count; i++)
            {
                _items[i].PlayFadeIn().Forget(cancellationToken);

                await UniTask.Delay(150, cancellationToken: cancellationToken);
            }
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(ANIMATION_FADE_OUT).ToUniTask(cancellationToken: cancellationToken);
        }
    }
}