using System.Collections.Generic;
using System.Threading;
using _FeatureHub.Attributes.Core;
using AssetKits.ParticleImage;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Utilities;
using PrimeTween;
using Sirenix.OdinInspector;
using Spine.Unity;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIPanelCongratulation : UIBasePanel
    {
        private readonly int _hashFadeIn = Animator.StringToHash("UI_FadeIn");
        private readonly int _hashFadeOut = Animator.StringToHash("UI_FadeOut");

        [FoldoutGroup("Animator")]
        [SerializeField, ReferenceValue]
        private Animator _animator;

        [FoldoutGroup("Image Effect")]
        [SerializeField, ReferenceValue("ShockwaveFX")]
        private ParticleImage _shockwaveFX;

        [FoldoutGroup("Image Effect")]
        [SerializeField, ReferenceValue("ParticlesFX")]
        private ParticleImage _particlesFX;

        [FoldoutGroup("UI Component")]
        [SerializeField, ReferenceValue("ContainerRewards")]
        private GridLayoutGroup _rewardsContainer;

        [SerializeField, ReferenceValue("btnClose")]
        private Button _btnClose;

        [SerializeField, ReferenceValue("@_btnClose/Contents")]
        private Graphic _graphicClose;

        [FoldoutGroup("Chest Animation")]
        [SerializeField, ReferenceValue("ChestContainer")]
        private GameObject _chestContainer;

        [FoldoutGroup("Chest Animation")]
        [SerializeField, ReferenceValue("@_chestContainer/ChestSpine")]
        private SkeletonGraphic _chestSpine;

        [FoldoutGroup("Chest Animation")]
        [SerializeField, SpineAnimation]
        private string _chestOpenAnimation;

        [FoldoutGroup("Chest Animation")]
        [SerializeField, SpineAnimation]
        private string _chestIdleAnimation;

        #region Runtime

        private List<RewardData> _rewardData;
        private IContainRewardTarget[] _targetPanels;
        private CancellationTokenSource _rewardAnimationCts;
        private bool _isChestOpening;

        #endregion

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hashFadeIn, 0.6f);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        protected override void OnBeforeFocus()
        {
            _btnClose.onClick.AddListener(CloseOnClicked);
            _btnClose.SetActive(false);

            pArgs.TryGetData("target_panels", out _targetPanels);
            pArgs.TryGetData("reward_data", out _rewardData);
            pArgs.TryGetData("show_chest", out _isChestOpening);

            // Setup chest if needed
            if (_isChestOpening && _chestContainer)
            {
                _chestContainer.SetActive(true);
                if (_chestSpine)
                {
                    _chestSpine.AnimationState.SetAnimation(0, _chestIdleAnimation, true);
                }
            }
            else if (_chestContainer)
            {
                _chestContainer.SetActive(false);
            }
        }

        protected override void OnAfterFocus()
        {
            if (_isChestOpening)
            {
                // Play chest opening animation first
                PlayChestOpeningAnimation().Forget();
            }
            else
            {
                // Show rewards directly
                ShowRewards();
            }
        }

        protected override void OnBeforeLostFocus()
        {
            // Cancel any ongoing reward animations
            _rewardAnimationCts?.Cancel();
            _rewardAnimationCts = null;

            _shockwaveFX.Stop();
            _shockwaveFX.Clear();
            _particlesFX.Stop();
            _particlesFX.Clear();

            _btnClose.onClick.RemoveListener(CloseOnClicked);
        }

        private async UniTask PlayChestOpeningAnimation()
        {
            if (!_chestSpine)
            {
                ShowRewards();
                return;
            }

            var openTrack = _chestSpine.AnimationState.SetAnimation(0, _chestOpenAnimation, false);

            await UniTask.WaitForSeconds((float)openTrack.Animation.Duration * 0.8f);

            _shockwaveFX.Play();

            ShowRewards();

            _particlesFX.Play();
        }

        private void ShowRewards()
        {
            for (var i = 0; i < _rewardData.Count; i++)
            {
                var rewardItem = UIShortcut.SpawnRewardItem(_rewardData[i], _rewardsContainer.transform);
                rewardItem.CachedRectTransform.localScale = Vector3.one;
                rewardItem.CachedRectTransform.position = Vector3.zero;
                rewardItem.Setup(_rewardData[i]);
                rewardItem.PlayAppearAnimation(0.1f * i);
            }

            // Show tap to continue button after a delay
            Tween.Delay(0.5f + 0.1f * _rewardData.Count, ActiveTapToContinue);
        }

        private void ActiveTapToContinue()
        {
            _btnClose.SetActive(true);
            Tween.Alpha(_graphicClose, 0, 1, 0.5f);
        }

        private void CloseOnClicked()
        {
            if (_targetPanels != null && _targetPanels.Length > 0)
            {
                // Let the UIObtainRewardPanel handle the animations
                UIShortcut.ObtainRewardsAsync(_rewardData, _targetPanels).Forget();
            }
            else
            {
                DataShortcut.ClaimRewards(_rewardData);
            }
            
            Close();
        }
    }
}