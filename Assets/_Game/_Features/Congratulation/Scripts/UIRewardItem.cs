using System;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Utilities;
using PrimeTween;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIRewardItem : MonoBehaviour
    {
        [SerializeField]
        private Image _icon;

        [SerializeField]
        private TMP_Text _labelQuantity;
        
        [SerializeField]
        private ObjectAttractor _attractor;
        
        public ObjectAttractor Attractor => _attractor;

        private RectTransform _rectTransform;
        public RectTransform CachedRectTransform => _rectTransform ??= GetComponent<RectTransform>();

        public RewardData Data { get; private set; }

        public void Setup(RewardData rewardData)
        {
            Data = rewardData;

            switch (rewardData.RewardType)
            {
                case RewardType.Currency when rewardData.CurrencyType is CurrencyType.Coin:
                case RewardType.Currency:
                    _icon.sprite = Core.Definition.Currency.GetDefinition(rewardData.CurrencyType).icon;
                    _labelQuantity.text = $"x{rewardData.Quantity}";
                    break;
                case RewardType.InfiniteLive:
                    _icon.sprite = Core.Definition.Rewards.infiniteLive.icon;
                    var timeSpan = TimeSpan.FromSeconds(rewardData.Quantity);
                    _labelQuantity.text = timeSpan.TotalDays >= 1 ? $"{timeSpan.Days}d" : $"{timeSpan.Hours}h";
                    break;
                case RewardType.Booster:
                    _icon.sprite = Core.Definition.Booster.GetDefinition(rewardData.BoosterType).icon;
                    _labelQuantity.text = $"x{rewardData.Quantity}";
                    break;
                case RewardType.NoAds:
                    _icon.sprite = Core.Definition.Rewards.noAds.icon;
                    _labelQuantity.text = string.Empty;
                    break;
                case RewardType.Skin:
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        public void PlayIdleAnimation()
        {
            Tween.UIAnchoredPositionY(_icon.rectTransform, 15f, 1f, Ease.Linear, cycles: -1, CycleMode.Yoyo);
        }

        public void PlayAppearAnimation(float delay)
        {
            PlayIdleAnimation();
        }
    }
}