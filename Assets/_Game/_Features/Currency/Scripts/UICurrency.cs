using System.Collections.Generic;
using _FeatureHub.Attributes.Core;
using AssetKits.ParticleImage;
using OnePuz.Attributes;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using PrimeTween;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

namespace OnePuz.UI
{
    public class UICurrency : MonoBehaviour, IRewardTarget
    {
        [Title("General")]
        [SerializeField]
        private CurrencyType _currencyType = CurrencyType.Coin;

        [SerializeField]
        private TMP_Text _quantityText;

        [Title("Collecting")]
        [SerializeField, ReferenceValue(manually: true)]
        private Animator _animator;

        [SerializeField]
        private AnimationClip _collectedAnimation;

        [SerializeField]
        private ParticleImage _collectVFX;

        [Space]
        [SerializeField]
        private RectTransform _currencyIconRectTransform;

        public RectTransform CurrencyIconRectTransform => _currencyIconRectTransform;

        public SimpleEnumCallback<CurrencyType> onClick;

        private Tween _quantityTween;
        private int _currentQuantity;

        private void Awake()
        {
            this.EventSubscribe<CurrencyChangedEvent>(OnCurrencyChanged);
        }

        private void OnCurrencyChanged(CurrencyChangedEvent e)
        {
            if (!e.isInstant) return;
            _currentQuantity = DataShortcut.Currency.GetCurrency(_currencyType);
            UpdateQuantity(_currentQuantity, true);
        }

        public void Init()
        {
            _currentQuantity = DataShortcut.Currency.GetCurrency(_currencyType);
            UpdateQuantity(_currentQuantity, true);
        }

        public void Ins_OnClick()
        {
            var args = new PanelArgs
            {
                data = new Dictionary<string, object>
                {
                    { "currency", _currencyType }
                }
            };
            UIShortcut.ShowPanel(UIKeys.Panel.SHOP, args);

            onClick?.Invoke(_currencyType);
            
            Core.Event.Fire(new OnClickAddCurrencyButton
            {
                Type = _currencyType,
                TargetAtractor = _currencyIconRectTransform
            });
        }

        private void UpdateQuantity(int targetQuantity, bool instant = false)
        {
            _quantityTween.Stop();

            if (instant)
            {
                _quantityText.text = targetQuantity.ToString("N0");
            }
            else
            {
                _quantityTween = Tween.Custom(_currentQuantity, targetQuantity, 1f, value =>
                {
                    _currentQuantity = (int)value;
                    _quantityText.text = _currentQuantity.ToString("N0");
                });
            }
        }

        private void PlayCollectedAnimation(int rewardAmount)
        {
            if (rewardAmount < 5 || Random.Range(0, 2) == 0)
                AudioShortcut.PlayCollectItem();

            _animator.PlayManual(Animator.StringToHash(_collectedAnimation.name));
            _collectVFX?.Play();
        }

        public Transform GetRewardTargetTransform()
        {
            return _currencyIconRectTransform;
        }

        public void OnRewardReachedTarget(int rewardAmount)
        {
            PlayCollectedAnimation(rewardAmount);

            UpdateQuantity(DataShortcut.Currency.GetCurrency(_currencyType));
        }

        public virtual void Appear()
        {
            // var currencyQuantity = DataShortcut.Currency.GetCurrency(_currencyType);
            // UpdateQuantity(currencyQuantity, instant: true);
        }

        public virtual void OnLastRewardReachedTarget()
        {
        }
    }
}
public struct OnClickAddCurrencyButton
{
    public CurrencyType Type;
    public Transform TargetAtractor;
}