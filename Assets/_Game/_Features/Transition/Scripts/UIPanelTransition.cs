using System.Threading;
using OnePuz.Attributes;
using OnePuz.Extensions;
using Cysharp.Threading.Tasks;
using PrimeTween;
using UnityEngine;
using Tween = PrimeTween.Tween;

namespace OnePuz.UI
{
    public class UIPanelTransition : UIBasePanel
    {
        [SerializeField, PreAssigned("SoftMask/Point")]
        private RectTransform _pointRectTransform;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            var currentSelectedGameObject = Core.UI.pEventSystem.currentSelectedGameObject;
            if (currentSelectedGameObject)
                _pointRectTransform.position = currentSelectedGameObject.transform.position;
            else
                _pointRectTransform.anchoredPosition = Vector3.zero;

            Tween.EulerAngles(_pointRectTransform, new Vector3(0, 0, -180f), Vector3.zero, 0.5f)
                .Forget(cancellationToken);

            _pointRectTransform.localScale = Vector3.one * 50f;
            await Tween.Scale(_pointRectTransform, 0f, 0.5f, ease: Ease.OutQuad).ToUniTask(cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            Tween.EulerAngles(_pointRectTransform, Vector3.zero, new Vector3(0f, 0f, 180f), 0.5f)
                .Forget(cancellationToken);

            _pointRectTransform.localScale = Vector3.zero;
            _pointRectTransform.anchoredPosition = Vector3.zero;
            await Tween.Scale(_pointRectTransform, 50f, 0.5f, ease: Ease.InQuad).ToUniTask(cancellationToken);

            Core.Event.FireAsync(new TransitionState { State = 1 });
        }
    }

    public struct TransitionState
    {
        public int State; //0=show 1=hide
    }
}