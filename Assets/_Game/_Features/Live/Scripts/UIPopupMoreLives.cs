using System;
using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.Live.UI
{
    public class UIPopupMoreLives : UIBasePanel
    {
        private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");
        private readonly int _hideAnimation = Animator.StringToHash("Popup_TransitionOut");

        [Header("Popup Animation")] [SerializeField, ReferenceValue()]
        private Animator _animator;

        [Header("Elements")] [SerializeField, ReferenceValue("ButtonClose")]
        private Button _closeButton;

        [SerializeField, ReferenceValue("GroupButtons")]
        private GameObject _groupButtons;

        [SerializeField, ReferenceValue("ButtonBuy")]
        private Button _buyButton;

        [SerializeField, ReferenceValue("ButtonBuy/LabelCost")]
        private TextMeshProUGUI _labelCost;

        [SerializeField, ReferenceValue("ButtonBuy/LabelLiveAmount")]
        private TextMeshProUGUI _labelLiveAmount_Coin;

        [SerializeField, ReferenceValue("ButtonWatch")]
        private Button _watchButton;

        [SerializeField, ReferenceValue("ButtonWatch/LabelLiveAmount")]
        private TextMeshProUGUI _labelLiveAmount_Ads;

        [SerializeField, ReferenceValue("Frame/LabelLive")]
        private TextMeshProUGUI _labelCurrentLives;

        [SerializeField, ReferenceValue("Frame/ImgInfinite")]
        private Image _iconInfinite;

        [SerializeField, ReferenceValue("PanelTime")]
        private GameObject _panelTime;

        [SerializeField, ReferenceValue("PanelTime/LabelTime")]
        private TextMeshProUGUI _labelTime;

        [SerializeField, ReferenceValue("PanelTime/Label")]
        private TextMeshProUGUI _labelTimeMessage;

        [SerializeField, ReferenceValue("Frame/Space")]
        private GameObject _panelTimeSpace;

        [SerializeField, ReferenceValue("LabelMessage")]
        private TextMeshProUGUI _labelMessage;

        private LiveService _service;

        public override void Init(string id)
        {
            base.Init(id);

            _service = Core.Live;
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            UpdateUI();
        }

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();

            this.EventSubscribe<EventLiveChanged>(OnLiveChanged);

            _closeButton.onClick.AddListener(OnClickedCloseButton);
            _watchButton.onClick.AddListener(OnClickedWatchButton);
            _buyButton.onClick.AddListener(OnClickedBuyButton);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _closeButton.onClick.RemoveListener(OnClickedCloseButton);
            _watchButton.onClick.RemoveListener(OnClickedWatchButton);
            _buyButton.onClick.RemoveListener(OnClickedBuyButton);

            this.EventUnsubscribe<EventLiveChanged>(OnLiveChanged);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        private void OnLiveChanged(EventLiveChanged e)
        {
            if (_service == null)
                return;

            UpdateUI();
        }

        private void UpdateUI()
        {
            _labelCurrentLives.text = _service.CurrentLive.ToString();

            _groupButtons.SetActive(_service.IsRefilling);
            _panelTime.SetActive(_service.IsInfinite || _service.IsRefilling);
            _panelTimeSpace.SetActive(!_service.IsInfinite && !_service.IsRefilling);
            _labelMessage.gameObject.SetActive(!_service.IsRefilling);
            _labelCurrentLives.gameObject.SetActive(!_service.IsInfinite);
            _iconInfinite.enabled = _service.IsInfinite;

            if (_service.IsInfinite)
            {
                _labelMessage.text = $"You have Infinite Lives!";

                var timeLeftString = TimeFormatter.FormatSmart(_service.TimeLeftToInfiniteLive);
                _labelTime.text = timeLeftString;
                _labelTimeMessage.text = "Time left:";
            }
            else if (_service.IsRefilling)
            {
                _labelTime.text = $"{_service.TimeLeftToRefill:mm\\m\\:ss\\s}";
                _labelTimeMessage.text = "Next live in:";

                _labelLiveAmount_Ads.text = "x1";
                _labelLiveAmount_Coin.text = $"x{Core.Definition.Live.maxLive - _service.CurrentLive}";

                _labelCost.text =
                    $"{Core.Definition.Live.costPerLive * (Core.Definition.Live.maxLive - _service.CurrentLive)}";
            }
            else
            {
                _labelMessage.text = $"You have FULL lives!";
            }
        }

        private void OnClickedCloseButton()
        {
            Close();
        }

        private void OnClickedBuyButton()
        {
            var price = Core.Definition.Live.costPerLive * (Core.Definition.Live.maxLive - _service.CurrentLive);
            if (DataShortcut.Currency.GetCurrency(CurrencyType.Coin) < price)
            {
                UIShortcut.ShowPanel(UIKeys.Panel.SHOP,
                    PanelArgs.Default.AddData<Action>("next_popup",
                        () => UIShortcut.ShowPopup(UIKeys.Panel.POPUP_MORE_LIVES)));
                Close();
                return;
            }

            DataShortcut.Currency.AddCurrency(CurrencyType.Coin, -price);
            DataShortcut.Live.FinishRefill();
            _service.EarnLive(Core.Definition.Live.maxLive - _service.CurrentLive);
            Close();
        }

        private void OnClickedWatchButton()
        {
            if (!Core.Ads.IsRewardedVideoLoaded())
            {
                UIShortcut.ShowNoAdsAvailable();
                return;
            }

            Core.Ads.SubscribeRewardedVideoWatchedCallback(OnAdsShown);
            Core.Ads.ShowRewardedVideo();
        }

        private void OnAdsShown(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(OnAdsShown);
            if (!succeed)
                return;

            var nextLive = _service.CurrentLive + 1;
            if (nextLive >= Core.Definition.Live.maxLive)
                DataShortcut.Live.FinishRefill();

            _service.EarnLive();
            Close();
        }
    }
}