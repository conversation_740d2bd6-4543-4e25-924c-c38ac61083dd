using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using UnityEngine;

namespace OnePuz.UI
{
    public static class UIShortcut
    {
        private static UIService _instance;
        public static UIService Instance => _instance ??= Core.Get<UIService>();

        #region General

        public static void ShowPanel(string panelKey, PanelArgs args = null) => Instance.ShowPanel(panelKey, args);
        public static void ClosePanel(string panelKey) => Instance.ClosePanel(panelKey);
        public static async UniTask ClosePanelAsync(string panelKey) => await Instance.CloseAsync(panelKey);
        public static void ShowPopup(string popupKey, PanelArgs args = null) => Instance.ShowPopup(popupKey, args);
        public static void EnqueuePopup(string popupKey) => Instance.ShowPopup(popupKey, PanelArgs.Popup.WithMode(ShowMode.APPEND));
        public static void ShowPopupHistory(string popupKey) => Instance.ShowPopup(popupKey, PanelArgs.Popup.WithMode(ShowMode.HISTORY));

        #endregion

        #region Transition

        public static async UniTask ShowTransitionAsync() => await Instance.ShowPanelAsync(UIKeys.Panel.TRANSITION);
        public static async UniTask CloseTransitionAsync() => await Instance.CloseAsync(UIKeys.Panel.TRANSITION);

        #endregion

        #region Obtaining Rewards

        private static UIObtainRewardPanel _obtainRewardPanel;

        private static UIObtainRewardPanel ObtainRewardPanel
        {
            get
            {
                if (_obtainRewardPanel != null) return _obtainRewardPanel;

                _obtainRewardPanel = Instance.Get<UIObtainRewardPanel>();
                Instance.ShowPanel(UIKeys.Panel.OBTAIN_REWARD);
                return _obtainRewardPanel;
            }
            set => _obtainRewardPanel = value;
        }

        public static void ShowCongratulationPanel<T>(List<RewardData> rewards, bool showChest = false) where T : class, IPanel, IContainRewardTarget
        {
            ShowCongratulationPanelAsync(rewards, new IContainRewardTarget[] { Instance.Get<T>() }, showChest).Forget();
        }

        public static async UniTask ShowCongratulationPanelAsync(List<RewardData> rewards, IContainRewardTarget[] targetPanels = null, bool showChest = false)
        {
            var args = PanelArgs.Popup.AddData("reward_data", rewards).AddData("show_chest", showChest);

            if (targetPanels != null && targetPanels.Length > 0)
            {
                args.data.Add("target_panels", targetPanels);
            }

            await Instance.ShowPopupAsync(UIKeys.Panel.PANEL_CONGRATULATION, args);
        }

        public static UIRewardItem SpawnRewardItem(RewardData rewardData, Transform container)
        {
            return ObtainRewardPanel.SpawnRewardItem(rewardData, container);
        }

        public static async UniTask ObtainRewardsAsync(List<RewardData> rewards, IContainRewardTarget[] targetPanels)
        {
            await ObtainRewardPanel.ObtainRewardsAsync(rewards, targetPanels);
        }

        #endregion

        public static void ShowMessage(string title, string message, ShowMode mode = ShowMode.HISTORY) => Instance.ShowPopup(
            UIKeys.Panel.POPUP_MESSAGE,
            PanelArgs.Popup
                .WithMode(mode)
                .AddData("message", message)
                .AddData("title", title));

        public static void ShowNoAdsAvailable()
            => ShowMessage(
                "No Ads Available",
                "We were unable to find any ads to show! Please try again shortly.");
    }
}