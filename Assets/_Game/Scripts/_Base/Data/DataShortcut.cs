using System;
using System.Collections.Generic;
using OnePuz.DailyReward.Data;
using OnePuz.Data.Services;
using OnePuz.Live;

namespace OnePuz.Data
{
    public static class DataShortcut
    {
        private static DataService _instance;
        public static DataService Instance => _instance ??= Core.Get<DataService>();

        public static UserData User => Instance.GetData<UserData>("UserData");
        public static LevelData Level => Instance.GetData<LevelData>("LevelData");
        public static BoosterData Booster => Instance.GetData<BoosterData>("BoosterData");
        public static SettingData Setting => Instance.GetData<SettingData>("SettingData");
        public static CurrencyData Currency => Instance.GetData<CurrencyData>("CurrencyData");
        public static DailyRewardData DailyReward => Instance.GetData<DailyRewardData>("DailyRewardData");
        public static RatingData Rating => Instance.GetData<RatingData>("RatingData");
        public static LevelChestData LevelChest => Instance.GetData<LevelChestData>("LevelChestData");
        public static AdsData Ads => Instance.GetData<AdsData>("AdsData");
        public static LiveData Live => Instance.GetData<LiveData>("LiveData");
        public static TutorialData Tutorial => Instance.GetData<TutorialData>("TutorialData");

        public static void ClaimRewards(List<RewardData> rewards, bool isInstant = true)
        {
            for (var i = 0; i < rewards.Count; i++)
            {
                var rewardData = rewards[i];

                switch (rewardData.RewardType)
                {
                    case RewardType.Currency:
                        Currency.AddCurrency(rewardData.CurrencyType, rewardData.Quantity, isInstant);
                        break;
                    case RewardType.Booster:
                        Booster.AddBooster(rewardData.BoosterType, rewardData.Quantity, isInstant);
                        break;
                    case RewardType.Skin:
                        break;
                    case RewardType.InfiniteLive:
                        Core.Live.EarnInfiniteLive(rewardData.Quantity);
                        Core.Event.Fire<EventLiveChanged>();
                        break;
                    case RewardType.NoAds:
                        if (!Ads.isAdsRemoved)
                        {
                            Ads.OnRemoveAds();
                            Core.Ads.HideBanner();
                            Core.Event.Fire(new OnRemoveAds());
                        }

                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }
    }
}