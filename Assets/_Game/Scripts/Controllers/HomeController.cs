using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.UI;
using UnityEngine;

namespace OnePuz.Controller
{
    public class HomeController : BaseController
    {
        private void Update()
        {
            if (Input.GetKeyDown(KeyCode.Space))
            {
                var listReward = new List<RewardData>()
                {
                    new RewardData(CurrencyType.Coin, 100),
                    new RewardData(BoosterType.UNDO, 100),
                    // new RewardData(RewardType.InfiniteLive, 100)
                };
                UIShortcut.ShowPanel(UIKeys.Panel.PANEL_CONGRATULATION,
                    PanelArgs.Default.AddData("reward_data", listReward));
            }
            else if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                var listReward = new List<RewardData>()
                {
                    new RewardData(CurrencyType.Coin, 100),
                    new RewardData(BoosterType.UNDO, 1),
                    new RewardData(BoosterType.SHUFFLE, 2),
                    // new RewardData(RewardType.InfiniteLive, 100)
                };
                UIShortcut.ShowCongratulationPanel<UIPanelHome>(listReward);
            }
        }

        public override async UniTask LoadAsync()
        {
            Debug.Log("Home started");

            await UniTask.CompletedTask;
        }

        public override UniTask UnloadAsync()
        {
            return UniTask.CompletedTask;
        }

        public override void Activate()
        {
            AutoShowPopupsAsync().Forget();
        }

        public override UniTask ResetAsync()
        {
            return UniTask.CompletedTask;
        }

        private async UniTask AutoShowPopupsAsync()
        {
            var homeBuildingPanel = Core.UI.Get<UIPanelHome>();
            await UniTask.WaitUntil(() => homeBuildingPanel.pIsOpen);

            ShowDailyReward();
            ShowRating();
            ShowMoreLive();
        }

        private bool ShowDailyReward()
        {
            if (!DataShortcut.DailyReward.CanShowDailyRewardPopup) return false;
            UIShortcut.EnqueuePopup(UIKeys.Panel.DAILY_REWARDS);
            return true;
        }

        private bool ShowRating()
        {
            if (DataShortcut.Rating.alreadyShownRatingLevels.Contains(DataShortcut.Level.Current))
                return false;

            if (!DataShortcut.Rating.ratingLevelList.Contains(DataShortcut.Level.Current)) return false;
            if (DataShortcut.Rating.alreadyRated || !DataShortcut.Rating.enableRatingPopup)
                return false;

            DataShortcut.Rating.alreadyShownRatingLevels.Add(DataShortcut.Level.Current);
            UIShortcut.EnqueuePopup(UIKeys.Panel.RATING);
            return true;
        }

        private bool ShowMoreLive()
        {
            if (Core.State.PreviousState == GameState.LOSE)
            {
                UIShortcut.EnqueuePopup(UIKeys.Panel.POPUP_MORE_LIVES);
            }
            return true;
        }

        public void Ins_OnBattleClicked()
        {
            Core.Game();
        }
    }
}