using System;
using System.Collections.Generic;
using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Services;
using PrimeTween;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIPopupNewBooster : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");
        private readonly int hashFadeOut = Animator.StringToHash("Popup_TransitionOut");

        [SerializeField, ReferenceValue]
        private Animator m_Animator;

        [SerializeField, ReferenceValue("Popup/ButtonClaim")]
        private Button m_BtnClaim;

        [SerializeField, ReferenceValue("Title/Label")]
        private TMP_Text m_Title;
        
        [Serial<PERSON><PERSON><PERSON>, ReferenceValue("Description")]
        private TMP_Text m_Description;
        
        [Serial<PERSON><PERSON><PERSON>, ReferenceValue("@m_BtnClaim/IconBooster")]
        private Image m_IconBooster;

        #region Runtime variables

        private BoosterType m_BoosterType;

        #endregion

        public override void Init(string id)
        {
            base.Init(id);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await m_Animator.PlayManual(hashFadeOut);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await m_Animator.PlayManual(hashFadeIn);
        }

        protected override void OnBeforeFocus()
        {
            m_BoosterType = pArgs.GetData<BoosterType>("booster_type");
            
            var asset = Core.Definition.Booster.GetDefinition(m_BoosterType);
            m_IconBooster.sprite = asset.icon;
            m_Title.text = asset.displayName;
            m_Description.text = asset.description;
            
            Tween.Scale(m_BtnClaim.transform, Vector3.one, 1.1f * Vector3.one, 0.5f, Ease.InOutSine, cycles: -1, CycleMode.Yoyo);
        }

        protected override void OnAfterFocus()
        {
            m_BtnClaim.onClick.AddListener(ClaimOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            Tween.StopAll(m_BtnClaim.transform);
            m_BtnClaim.onClick.RemoveListener(ClaimOnClick);
        }

        private void ClaimOnClick()
        {
            DataShortcut.Tutorial.Save(m_BoosterType switch
            {
                BoosterType.UNDO => TutorialData.TutorialStep.GUIDE_UNDO,
                BoosterType.MORE_SLOTS => TutorialData.TutorialStep.GUIDE_MORE_SLOTS,
                BoosterType.SHUFFLE => TutorialData.TutorialStep.GUIDE_SHUFFLE,
                BoosterType.MAGNET => TutorialData.TutorialStep.GUIDE_MAGNET,
                _ => throw new ArgumentOutOfRangeException()
            });
            
            UIShortcut.ShowCongratulationPanel<UIPanelGame>(new List<RewardData>
            {
                new(m_BoosterType, 1)
            });
            
            Close();
        }

    }
}