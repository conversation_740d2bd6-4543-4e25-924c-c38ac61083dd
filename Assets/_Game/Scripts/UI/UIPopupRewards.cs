using UnityEngine;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine.UI;
using System.Collections.Generic;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.UI.Navigation;
using OnePuz.OPTimeline;
using System;
using System.Linq;
using TMPro;

namespace OnePuz.UI
{
    public class UIPopupRewards : UIBasePanel
    {
        [Header("Popup Animation")]
        [SerializeField]
        private OPAnimatorPlayer _showAnimation;
        [SerializeField]
        private OPAnimatorPlayer _bonusTitleAnimation;
        [SerializeField]
        private OPAnimatorPlayer _continueButtonAnimation;
        [SerializeField]
        private OPAnimatorPlayer _closeAnimation;

        [Header("Rewards")]
        [SerializeField]
        private Image _mainRewardIcon;
        [SerializeField]
        private Image _mainRewardShineIcon;
        [SerializeField]
        private TMP_Text _mainRewardQuantity;

        [SerializeField]
        private RectTransform _rewardContainer;
        [SerializeField]
        private GameObject _rewardItemPrefab;

        [Header("Elements")]
        [SerializeField]
        private Button _closeButton;
        [SerializeField]
        private Button _claimX2Button;

        
        private bool _adsOnce;
        private readonly List<UIPopupReward_Item> _items = new();

        private void Start()
        {
            Core.GlobalPool.WarmupCapacity(_rewardItemPrefab, 5);
        }

        private void SetupItems()
        {
            foreach (var item in _items)
                Core.GlobalPool.Recycle(item.gameObject);

            _items.Clear();

            var rewards = pArgs.GetData<List<RewardData>>("rewards");

            // Move gem reward to the first index
            var gemRewardIndex = rewards.FindIndex(x => x.RewardType == RewardType.Currency && x.CurrencyType == CurrencyType.Gem);
            if (gemRewardIndex >= 0)
                (rewards[0], rewards[gemRewardIndex]) = (rewards[gemRewardIndex], rewards[0]);

            for (var i = 0; i < rewards.Count; i++)
            {
                var reward = rewards[i];
                if (i == 0)
                {
                    var definition = Core.GetRewardDefinition(reward);
                    _mainRewardIcon.sprite = definition.icon;
                    _mainRewardShineIcon.sprite = definition.icon;
                    _mainRewardQuantity.text = $"X{reward.Quantity}";
                }
                else
                {
                    var rewardItem = Core.GlobalPool.Spawn(_rewardItemPrefab, _rewardContainer).GetComponent<UIPopupReward_Item>();
                    var definition = Core.GetRewardDefinition(reward);
                    rewardItem.Init(definition.icon, reward.Quantity);

                    _items.Add(rewardItem);
                }
            }
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            SetupItems();
        }

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();

            _closeButton.onClick.AddListener(OnCloseButtonClicked);
            _claimX2Button.onClick.AddListener(ClaimX2ButtonClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _closeButton.onClick.RemoveListener(OnCloseButtonClicked);
            _claimX2Button.onClick.RemoveListener(ClaimX2ButtonClicked);
        }

        private void ClaimX2ButtonClicked()
        {
            if (_adsOnce) return;
            _adsOnce = true;
            
            if (!Core.Ads.IsRewardedVideoLoaded())
            {
                UIShortcut.ShowNoAdsAvailable();
                return;
            }
            
            Core.Ads.SubscribeRewardedVideoWatchedCallback(OnAdsShowSuccess);
            Core.Ads.ShowRewardedVideo();
        }
        private void OnAdsShowSuccess(bool succeed)
        {
            _adsOnce = false;
            if (!succeed)
                return;
            Close();

            var screen = pArgs.GetData<string>("screen");
            var rewards = pArgs.GetData<List<RewardData>>("rewards");

            foreach (var rw in rewards.Where(rw => rw.RewardType is RewardType.Currency or RewardType.Booster))
            {
                rw.Quantity *= 2;
            }
            
            switch (screen)
            {
                case UIKeys.Panel.SHOP:
                    UIShortcut.ShowCongratulationPanel<UINavigationPage_Shop>(rewards);
                    break;
                case UIKeys.Panel.HOME:
                    UIShortcut.ShowCongratulationPanel<UINavigationPage_Main>(rewards);
                    break;
            }
        }
        private void OnCloseButtonClicked()
        {
            Close();

            var screen = pArgs.GetData<string>("screen");
            var rewards = pArgs.GetData<List<RewardData>>("rewards");

            switch (screen)
            {
                case UIKeys.Panel.SHOP:
                    UIShortcut.ShowCongratulationPanel<UINavigationPage_Shop>(rewards);
                    break;
                case UIKeys.Panel.HOME:
                    UIShortcut.ShowCongratulationPanel<UINavigationPage_Main>(rewards);
                    break;
            }
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            _continueButtonAnimation.Reset();
            _bonusTitleAnimation.Reset();
            _showAnimation.Play();

            await UniTask.Delay(TimeSpan.FromSeconds(_showAnimation.FullDuration), cancellationToken: cancellationToken);

            if (_items.Count > 0)
            {
                _bonusTitleAnimation.Play();
                await UniTask.Delay(TimeSpan.FromSeconds(_bonusTitleAnimation.FullDuration / 2f), cancellationToken: cancellationToken);

                foreach (var t in _items)
                {
                    t.PlayAnimation();

                    await UniTask.Delay(250, cancellationToken: cancellationToken);
                }
            }

            _continueButtonAnimation.Play();
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            _closeAnimation.Play();
            await _closeAnimation.WaitForComplete();
        }
    }

}