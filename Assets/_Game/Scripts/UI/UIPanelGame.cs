using System;
using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIPanelGame : UIBasePanel, IContainRewardTarget
    {
        [SerializeField, ReferenceValue]
        private Animator _animator;
        
        [SerializeField, ReferenceValue("PanelTop/ButtonSettings")]
        private Button _btnSetting;

        [SerializeField, ReferenceValue("PanelBottom/ButtonUndo")]
        private UIBooster _boosterUndo;
        
        [SerializeField, ReferenceValue("PanelBottom/ButtonMoreSlot")]
        private UIBooster _boosterMoreSlot;

        [SerializeField, ReferenceValue("PanelBottom/ButtonShuffle")]
        private UIBooster _boosterShuffle;

        [Serial<PERSON>Field, ReferenceValue("PanelBottom/ButtonMagnet")]
        private UIBooster _boosterMagnet;
        
        [SerializeF<PERSON>, ReferenceValue("ButtonWin")]
        private Button _buttonWin;
        
        [Serial<PERSON>Field, ReferenceValue("ButtonLose")]
        private Button _buttonLose;
        
        [SerializeField, ReferenceValue("ButtonReplay")]
        private Button _buttonReplay;

        [SerializeField, ReferenceValue("PanelTop/PanelCoin")]
        private UICurrency _panelCoin;

        [SerializeField, ReferenceValue("PanelTop/LabelLevel")]
        private TMP_Text _labelLevel;
  
        private readonly int _showAnimation = Animator.StringToHash("PanelGame_Show");
        
        public override void Init(string id)
        {
            base.Init(id);

            this.EventSubscribe<BoosterChangedEvent>(HandleOnChangedBooster);
            this.EventSubscribe<GameStateChangedEvent>(HandleOnGameStateChanged);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayReverse(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        public void FadeIn()
        {
            _animator.PlayManual(_showAnimation, 0.5f)
                .OnComplete(this, @this => @this.pRaycaster.enabled = true);
        }

        public void FadeOut()
        {
            pRaycaster.enabled = false;
            _animator.PlayReverse(_showAnimation, 0.5f);
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            Setup();
        }

        private void Setup()
        {
            _labelLevel.text = $"Level {DataShortcut.Level.Current + 1}";
            _boosterUndo.Init(BoosterType.UNDO);
            _boosterMoreSlot.Init(BoosterType.MORE_SLOTS);
            _boosterShuffle.Init(BoosterType.SHUFFLE);
            _boosterMagnet.Init(BoosterType.MAGNET);
            _panelCoin.Init();
        }

        protected override void OnAfterFocus()
        {
            _btnSetting.onClick.AddListener(SettingOnClick);

            _boosterUndo._clickHandler.onClick.AddListener(UndoOnClicked);
            _boosterMoreSlot._clickHandler.onClick.AddListener(MoreSlotsOnClicked);
            _boosterShuffle._clickHandler.onClick.AddListener(ShuffleOnClicked);
            _boosterMagnet._clickHandler.onClick.AddListener(MagnetOnClicked);
            
            _buttonWin.onClick.AddListener(WinOnClicked);
            _buttonLose.onClick.AddListener(LoseOnClicked);
            _buttonReplay.onClick.AddListener(ReplayOnClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnSetting.onClick.RemoveListener(SettingOnClick);
            
            _boosterUndo._clickHandler.onClick.RemoveListener(UndoOnClicked);
            _boosterMoreSlot._clickHandler.onClick.RemoveListener(MoreSlotsOnClicked);
            _boosterShuffle._clickHandler.onClick.RemoveListener(ShuffleOnClicked);
            _boosterMagnet._clickHandler.onClick.RemoveListener(MagnetOnClicked);
            
            _buttonWin.onClick.RemoveListener(WinOnClicked);
            _buttonLose.onClick.RemoveListener(LoseOnClicked);
            _buttonReplay.onClick.RemoveListener(ReplayOnClicked);
        }

        private void SettingOnClick() => UIShortcut.ShowPopup(UIKeys.Panel.PAUSE);
        
        private void UndoOnClicked()
        {
            if (_boosterUndo._isLocked)
            {
                BlockIndicator.Toast($"Unlock at level {Core.Definition.Booster.GetDefinition(BoosterType.UNDO).levelUnlock + 1}");
                return;
            }
        }

        private void MoreSlotsOnClicked()
        {
            if (_boosterMoreSlot._isLocked)
            {
                BlockIndicator.Toast($"Unlock at level {Core.Definition.Booster.GetDefinition(BoosterType.MORE_SLOTS).levelUnlock + 1}");
                return;
            }
        }

        private void ShuffleOnClicked()
        {
            if (_boosterShuffle._isLocked)
            {
                BlockIndicator.Toast($"Unlock at level {Core.Definition.Booster.GetDefinition(BoosterType.SHUFFLE).levelUnlock + 1}");
                return;
            }
        }

        private void MagnetOnClicked()
        {
            if (_boosterMagnet._isLocked)
            {
                BlockIndicator.Toast($"Unlock at level {Core.Definition.Booster.GetDefinition(BoosterType.MAGNET).levelUnlock + 1}");
                return;
            }
        }
        
        private void ReplayOnClicked()
        {
            Core.Replay();
        }

        private void LoseOnClicked()
        {
            Core.Lose();
        }

        private void WinOnClicked()
        {
            Core.Win();
        }

        private void HandleOnChangedBooster(BoosterChangedEvent e)
        {
            if (!e.isInstant)
                return;

            _boosterUndo.UpdateStatus();
            _boosterMoreSlot.UpdateStatus();
            _boosterShuffle.UpdateStatus();
            _boosterMagnet.UpdateStatus();
        }
    

        private void HandleOnGameStateChanged(GameStateChangedEvent e)
        {
            if (e.currentState != GameState.PLAYING) return;
            Setup();
        }

        public IRewardTarget GetRewardTarget<T>(T type) where T : Enum
        {
            if (typeof(T) == typeof(CurrencyType))
            {
                var currencyType = (CurrencyType)(object)type;
                return currencyType == CurrencyType.Coin ? _panelCoin : null;
            }
            else if (typeof(T) == typeof(BoosterType))
            {
                var boosterType = (BoosterType)(object)type;
                return boosterType switch
                {
                    BoosterType.UNDO => _boosterUndo,
                    BoosterType.MORE_SLOTS => _boosterMoreSlot,
                    BoosterType.SHUFFLE => _boosterShuffle,
                    BoosterType.MAGNET => _boosterMagnet,
                    _ => (IRewardTarget)null
                };
            }
            
            return null;
        }
    }
}