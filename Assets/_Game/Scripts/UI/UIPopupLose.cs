using System;
using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupLose : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");
        private readonly int hashFadeOut = Animator.StringToHash("Popup_TransitionOut");

        [SerializeField, ReferenceValue] private Animator m_Animator;

        [SerializeField, ReferenceValue("Popup/ButtonClose")]
        private Button m_BtnClose;

        [SerializeField, ReferenceValue("Popup/ButtonRetry")]
        private Button m_BtnRetry;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await m_Animator.PlayManual(hashFadeIn);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await m_Animator.PlayManual(hashFadeOut);
        }

        protected override void OnBeforeFocus()
        {
        }

        protected override void OnAfterFocus()
        {
            m_BtnClose.onClick.AddListener(CloseOnClick);
            m_BtnRetry.onClick.AddListener(RetryOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            m_BtnClose.onClick.RemoveListener(CloseOnClick);
            m_BtnRetry.onClick.RemoveListener(RetryOnClick);
        }

        private void CloseOnClick()
        {
            Core.Ads.ShowInterstitial();
            Core.Home();
        }

        private void RetryOnClick()
        {
            var currency = DataShortcut.Currency.GetCurrency(CurrencyType.Live);
            if (currency > 0)
            {
                Core.Ads.ShowInterstitial();
                Close();
                Core.Live.LostLive();
                Core.Replay();
            }
            else
            {
                Core.Home();
            }
        }
    }
}