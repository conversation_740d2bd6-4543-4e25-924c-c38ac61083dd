using System.Collections;
using System.Collections.Generic;
using System.Threading;
using _FeatureHub.Attributes.Core;
using _FeatureHub.PrimeTweenExtra;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.Data;
using OnePuz.Shop.Definition;
using OnePuz.Shop.UI;
using OnePuz.UI;
using UnityEngine;
using UnityEngine.UI;

public class UIPopupRemoveAds : UIBasePanel
{
    private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");
    private readonly int _hideAnimation = Animator.StringToHash("Popup_TransitionOut");

    [Header("Popup Animation")] [SerializeField, PreAssigned()]
    private Animator _animator;

    [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
    private Button _closeButton;

    [SerializeField, ReferenceValue("Popup")]
    private UIShopItem _itemNoAds;

    protected override void OnBeforeFocus()
    {
        base.OnBeforeFocus();
        _itemNoAds.Init(Core.Definition.Shop.noAdsBundles);
        _itemNoAds.onPurchasedSuccessfully += OnPurchasedNoAdsSuccessfully;
    }

    private void OnPurchasedNoAdsSuccessfully(ShopItemData data)
    {
        _itemNoAds.onPurchasedSuccessfully -= OnPurchasedNoAdsSuccessfully;
        DataShortcut.Ads.OnRemoveAds();
        Core.Event.Fire(new OnRemoveAds());
        
        var shopItemsDict = Core.Definition.Shop.GetShopItemDictionary();
        if (!shopItemsDict.TryGetValue(data.productId, out var purchasedItem)) return;
        var listReward = purchasedItem.rewards;
        Close();
        Core.UI.ShowPanel(UIKeys.Panel.PANEL_CONGRATULATION,
            PanelArgs.Default.AddData("reward_data", listReward));
    }

    protected override void OnAfterFocus()
    {
        base.OnAfterFocus();


        _closeButton.onClick.AddListener(OnCloseButtonClicked);
    }

    protected override void OnBeforeLostFocus()
    {
        base.OnBeforeLostFocus();


        _closeButton.onClick.RemoveListener(OnCloseButtonClicked);
    }

    private void OnCloseButtonClicked()
    {
        Close();
    }


    protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
    {
        await _animator.PlayManual(_showAnimation);
    }

    protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
    {
        await UniTask.CompletedTask;
    }
}

public struct OnRemoveAds
{
}