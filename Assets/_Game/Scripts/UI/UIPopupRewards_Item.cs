using UnityEngine;
using UnityEngine.UI;
using TMPro;
using OnePuz.OPTimeline;

namespace OnePuz.UI
{
    public class UIPopupReward_Item : MonoBehaviour
    {
        [Header("Elements")]
        [SerializeField]
        private Image _iconImage;
        [SerializeField]
        private TMP_Text _quantityText;

        [Head<PERSON>("Animations")]
        [SerializeField]
        private OPAnimatorPlayer _showAnimation;

        public void Init(Sprite icon, int amount)
        {
            _iconImage.sprite = icon;
            _quantityText.text = $"X{amount}";

            _showAnimation.Reset();
        }

        public void PlayAnimation()
        {
            _showAnimation.Play();
        }
    }

}