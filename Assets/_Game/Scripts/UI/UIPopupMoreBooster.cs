using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupMoreBooster : UIBasePanel
    {
        private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");
        private readonly int _hideAnimation = Animator.StringToHash("Popup_TransitionOut");

        [Header("Popup Animation")]
        [SerializeField, PreAssigned()]
        private Animator _animator;

        [BoxGroup("Ads"), SerializeField, PreAssigned("Container/Popup/PanelAds/ButtonBuy")]
        private Button _buyButtonByAds;

        [BoxGroup("Ads"), SerializeField, PreAssigned("Container/Popup/PanelAds/Icon")]
        private Image _boosterIconByAds;

        [BoxGroup("Ads"), SerializeField, PreAssigned("Container/Popup/PanelAds/LabelQuantity")]
        private TMP_Text _boosterQuantityByAds;

        [FormerlySerializedAs("_buyButton")]
        [BoxGroup("Coin"), SerializeField, PreAssigned("Container/Popup/PanelCoin/ButtonBuy")]
        private Button _buyButtonByCoin;
        
        [BoxGroup("Coin"), SerializeField, PreAssigned("Container/Popup/PanelCoin/ButtonBuy/LabelPrice")]
        private TMP_Text _labelPrice;

        [BoxGroup("Coin"), SerializeField, PreAssigned("Container/Popup/PanelCoin/Icon")]
        private Image _boosterIconByCoin;

        [BoxGroup("Coin"), SerializeField, PreAssigned("Container/Popup/PanelCoin/LabelQuantity")]
        private TMP_Text _boosterQuantityByCoin;

        [Header("Elements")]
        [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
        private Button _closeButton;

        [SerializeField, PreAssigned("Container/Popup/LabelTitle")]
        private TMP_Text _titleLabel;

        private BoosterType _boosterType;
        private int _rewardAmount;
        
        private const int PRICE = 500;

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            pArgs.TryGetData("boosterType", out _boosterType);
            
            _rewardAmount = _boosterType == BoosterType.UNDO ? 3 : 2;

            var boosterDefinition = Core.Definition.Booster.GetDefinition(_boosterType);

            _titleLabel.text = boosterDefinition.displayName;

            _boosterIconByAds.sprite = boosterDefinition.icon;
            _boosterIconByCoin.sprite = boosterDefinition.icon;

            _boosterQuantityByAds.text = "x1";
            _boosterQuantityByCoin.text = $"x{_rewardAmount}";
            
            _labelPrice.text = $"{PRICE}";

            _buyButtonByAds.onClick.AddListener(OnWatchButtonClicked);
            _buyButtonByCoin.onClick.AddListener(OnBuyButtonClicked);
            _closeButton.onClick.AddListener(OnCloseClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _buyButtonByAds.onClick.RemoveListener(OnWatchButtonClicked);
            _buyButtonByCoin.onClick.RemoveListener(OnBuyButtonClicked);
            _closeButton.onClick.RemoveListener(OnCloseClicked);
        }

        private void OnBuyButtonClicked()
        {
            if (DataShortcut.Currency.GetCurrency(CurrencyType.Coin) < PRICE)
            {
                UIShortcut.ShowPopup(UIKeys.Panel.POPUP_NOT_ENOUGH_COIN);
                return;
            }

            DataShortcut.Currency.AddCurrency(CurrencyType.Coin, -1 * PRICE);
            
            UIShortcut.ShowCongratulationPanel<UIPanelGame>(new List<RewardData>()
            {
                new ()
                {
                    RewardType = RewardType.Booster, BoosterType = _boosterType, Quantity = _rewardAmount
                }
            });
            Close();
        }

        private void OnWatchButtonClicked()
        {
            if (Core.Ads.IsRewardedVideoLoaded())
            {
                Core.Ads.SubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);
                Core.Ads.ShowRewardedVideo();

                var eventName = _boosterType == BoosterType.UNDO ? "rv_add_undo_ui" : "rv_add_bolt_ui";
                Core.Analytic.LogEvent(eventName, new Firebase.Analytics.Parameter[] {
                    new Firebase.Analytics.Parameter("level", DataShortcut.Level.Current + 1)
                });
            }
            else
            {
                Core.Ads.RequestRewardedVideo();
                
                UIShortcut.ShowNoAdsAvailable();
            }
        }
        
        private void HandleOnWatchVideoReward(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);

            if (!succeed) return;
            
            UIShortcut.ShowCongratulationPanel<UIPanelGame>(new List<RewardData>()
            {
                new ()
                {
                    RewardType = RewardType.Booster, BoosterType = _boosterType, Quantity = 1
                }
            });
            Close();
        }
        
        private void OnCloseClicked()
        {
            Close();
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hideAnimation);
        }
    }
}