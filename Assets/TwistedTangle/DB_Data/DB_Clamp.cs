using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Clamp : IMessage<DB_Clamp>, IMessage, IEquatable<DB_Clamp>, IDeepCloneable<DB_Clamp>, IBufferMessage
{
	private static readonly MessageParser<DB_Clamp> _parser;

	private UnknownFieldSet _unknownFields;

	public const int PositionFieldNumber = 1;

	private DB_Vector3float position_;

	public const int RotationFieldNumber = 2;

	private DB_Vector3float rotation_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_Clamp> Parser => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3float Position
	{
		get
		{
			return null;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Vector3float Rotation
	{
		get
		{
			return null;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Clamp()
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Clamp(DB_Clamp other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Clamp Clone()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override bool Equals(object other)
	{
		return false;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool Equals(DB_Clamp other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override int GetHashCode()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(DB_Clamp other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
