using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_ParticleWrapper : IMessage<DB_ParticleWrapper>, IMessage, IEquatable<DB_ParticleWrapper>, IDeepCloneable<DB_ParticleWrapper>, IBufferMessage
{
	private static readonly MessageParser<DB_ParticleWrapper> _parser;

	private UnknownFieldSet _unknownFields;

	public const int ParticlesFieldNumber = 1;

	private static readonly FieldCodec<DB_Particle> _repeated_particles_codec;

	private readonly RepeatedField<DB_Particle> particles_;

	public const int ScaleFieldNumber = 2;

	private float scale_;

	public const int ColorIdFieldNumber = 3;

	private int colorId_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageParser<DB_ParticleWrapper> Parser => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageDescriptor Descriptor => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	MessageDescriptor IMessage.Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Particle> Particles => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public float Scale
	{
		get
		{
			return 0f;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int ColorId
	{
		get
		{
			return 0;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_ParticleWrapper()
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_ParticleWrapper(DB_ParticleWrapper other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_ParticleWrapper Clone()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override bool Equals(object other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool Equals(DB_ParticleWrapper other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override int GetHashCode()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(DB_ParticleWrapper other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
