using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Level : IMessage<DB_Level>, IMessage, IEquatable<DB_Level>, IDeepCloneable<DB_Level>, IBufferMessage
{
	private static readonly MessageParser<DB_Level> _parser = new MessageParser<DB_Level>(() => new DB_Level());

	private UnknownFieldSet _unknownFields;

	public const int MapIdFieldNumber = 1;

	private int mapId_;

	public const int RopesDataFieldNumber = 2;

	private static readonly FieldCodec<DB_Rope> _repeated_ropesData_codec = FieldCodec.ForMessage(18, DB_Rope.Parser);

	private readonly RepeatedField<DB_Rope> ropesData_ = new RepeatedField<DB_Rope>();

	public const int GoldenRopesDataFieldNumber = 3;

	private static readonly FieldCodec<int> _repeated_goldenRopesData_codec = FieldCodec.ForInt32(26);

	private readonly RepeatedField<int> goldenRopesData_ = new RepeatedField<int>();

	public const int ClampsDataFieldNumber = 4;

	private static readonly FieldCodec<DB_Clamp> _repeated_clampsData_codec = FieldCodec.ForMessage(34, DB_Clamp.Parser);

	private readonly RepeatedField<DB_Clamp> clampsData_ = new RepeatedField<DB_Clamp>();

	public const int KeysDataFieldNumber = 5;

	private static readonly FieldCodec<DB_Key> _repeated_keysData_codec = FieldCodec.ForMessage(42, DB_Key.Parser);

	private readonly RepeatedField<DB_Key> keysData_ = new RepeatedField<DB_Key>();

	public const int IndexLockDataFieldNumber = 6;

	private static readonly FieldCodec<int> _repeated_indexLockData_codec = FieldCodec.ForInt32(50);

	private readonly RepeatedField<int> indexLockData_ = new RepeatedField<int>();

	public const int ColumnsDataFieldNumber = 7;

	private static readonly FieldCodec<DB_ColumnData> _repeated_columnsData_codec = FieldCodec.ForMessage(58, DB_ColumnData.Parser);

	private readonly RepeatedField<DB_ColumnData> columnsData_ = new RepeatedField<DB_ColumnData>();

	public const int IndexRemoveDataFieldNumber = 8;

	private static readonly FieldCodec<int> _repeated_indexRemoveData_codec = FieldCodec.ForInt32(66);

	private readonly RepeatedField<int> indexRemoveData_ = new RepeatedField<int>();

	public const int IdColorHolderFieldNumber = 9;

	private static readonly FieldCodec<int> _repeated_idColorHolder_codec = FieldCodec.ForInt32(74);

	private readonly RepeatedField<int> idColorHolder_ = new RepeatedField<int>();

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_Level> Parser => _parser;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => Descriptor;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public int MapId
	{
		get
		{
			return mapId_;
		}
		set
		{
			mapId_ = value;
		}
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Rope> RopesData => ropesData_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public RepeatedField<int> GoldenRopesData => goldenRopesData_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Clamp> ClampsData => clampsData_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Key> KeysData => keysData_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public RepeatedField<int> IndexLockData => indexLockData_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_ColumnData> ColumnsData => columnsData_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<int> IndexRemoveData => indexRemoveData_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public RepeatedField<int> IdColorHolder => idColorHolder_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Level()
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Level(DB_Level other) : this()
	{
		mapId_ = other.mapId_;
		ropesData_ = new RepeatedField<DB_Rope>();
		ropesData_.AddRange(other.ropesData_);
		goldenRopesData_ = new RepeatedField<int>();
		goldenRopesData_.AddRange(other.goldenRopesData_);
		clampsData_ = new RepeatedField<DB_Clamp>();
		clampsData_.AddRange(other.clampsData_);
		keysData_ = new RepeatedField<DB_Key>();
		keysData_.AddRange(other.keysData_);
		indexLockData_ = new RepeatedField<int>();
		indexLockData_.AddRange(other.indexLockData_);
		columnsData_ = new RepeatedField<DB_ColumnData>();
		columnsData_.AddRange(other.columnsData_);
		indexRemoveData_ = new RepeatedField<int>();
		indexRemoveData_.AddRange(other.indexRemoveData_);
		idColorHolder_ = new RepeatedField<int>();
		idColorHolder_.AddRange(other.idColorHolder_);
		_unknownFields = other._unknownFields;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Level Clone()
	{
		return new DB_Level(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override bool Equals(object other)
	{
		return Equals(other as DB_Level);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool Equals(DB_Level other)
	{
		if (ReferenceEquals(other, null))
		{
			return false;
		}
		if (ReferenceEquals(other, this))
		{
			return true;
		}
		if (MapId != other.MapId)
		{
			return false;
		}
		if (!ropesData_.Equals(other.ropesData_))
		{
			return false;
		}
		if (!goldenRopesData_.Equals(other.goldenRopesData_))
		{
			return false;
		}
		if (!clampsData_.Equals(other.clampsData_))
		{
			return false;
		}
		if (!keysData_.Equals(other.keysData_))
		{
			return false;
		}
		if (!indexLockData_.Equals(other.indexLockData_))
		{
			return false;
		}
		if (!columnsData_.Equals(other.columnsData_))
		{
			return false;
		}
		if (!indexRemoveData_.Equals(other.indexRemoveData_))
		{
			return false;
		}
		if (!idColorHolder_.Equals(other.idColorHolder_))
		{
			return false;
		}
		return Equals(_unknownFields, other._unknownFields);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override int GetHashCode()
	{
		int hash = 1;
		if (MapId != 0)
		{
			hash ^= MapId.GetHashCode();
		}
		hash ^= ropesData_.GetHashCode();
		hash ^= goldenRopesData_.GetHashCode();
		hash ^= clampsData_.GetHashCode();
		hash ^= keysData_.GetHashCode();
		hash ^= indexLockData_.GetHashCode();
		hash ^= columnsData_.GetHashCode();
		hash ^= indexRemoveData_.GetHashCode();
		hash ^= idColorHolder_.GetHashCode();
		if (_unknownFields != null)
		{
			hash ^= _unknownFields.GetHashCode();
		}
		return hash;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return JsonFormatter.Default.Format(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
		if (MapId != 0)
		{
			output.WriteRawTag(8);
			output.WriteInt32(MapId);
		}
		ropesData_.WriteTo(output, _repeated_ropesData_codec);
		goldenRopesData_.WriteTo(output, _repeated_goldenRopesData_codec);
		clampsData_.WriteTo(output, _repeated_clampsData_codec);
		keysData_.WriteTo(output, _repeated_keysData_codec);
		indexLockData_.WriteTo(output, _repeated_indexLockData_codec);
		columnsData_.WriteTo(output, _repeated_columnsData_codec);
		indexRemoveData_.WriteTo(output, _repeated_indexRemoveData_codec);
		idColorHolder_.WriteTo(output, _repeated_idColorHolder_codec);
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
		if (MapId != 0)
		{
			output.WriteRawTag(8);
			output.WriteInt32(MapId);
		}
		ropesData_.WriteTo(ref output, _repeated_ropesData_codec);
		goldenRopesData_.WriteTo(ref output, _repeated_goldenRopesData_codec);
		clampsData_.WriteTo(ref output, _repeated_clampsData_codec);
		keysData_.WriteTo(ref output, _repeated_keysData_codec);
		indexLockData_.WriteTo(ref output, _repeated_indexLockData_codec);
		columnsData_.WriteTo(ref output, _repeated_columnsData_codec);
		indexRemoveData_.WriteTo(ref output, _repeated_indexRemoveData_codec);
		idColorHolder_.WriteTo(ref output, _repeated_idColorHolder_codec);
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(ref output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		int size = 0;
		if (MapId != 0)
		{
			size += 1 + CodedOutputStream.ComputeInt32Size(MapId);
		}
		size += ropesData_.CalculateSize(_repeated_ropesData_codec);
		size += goldenRopesData_.CalculateSize(_repeated_goldenRopesData_codec);
		size += clampsData_.CalculateSize(_repeated_clampsData_codec);
		size += keysData_.CalculateSize(_repeated_keysData_codec);
		size += indexLockData_.CalculateSize(_repeated_indexLockData_codec);
		size += columnsData_.CalculateSize(_repeated_columnsData_codec);
		size += indexRemoveData_.CalculateSize(_repeated_indexRemoveData_codec);
		size += idColorHolder_.CalculateSize(_repeated_idColorHolder_codec);
		if (_unknownFields != null)
		{
			size += _unknownFields.CalculateSize();
		}
		return size;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(DB_Level other)
	{
		if (other == null)
		{
			return;
		}
		if (other.MapId != 0)
		{
			MapId = other.MapId;
		}
		ropesData_.Add(other.ropesData_);
		goldenRopesData_.Add(other.goldenRopesData_);
		clampsData_.Add(other.clampsData_);
		keysData_.Add(other.keysData_);
		indexLockData_.Add(other.indexLockData_);
		columnsData_.Add(other.columnsData_);
		indexRemoveData_.Add(other.indexRemoveData_);
		idColorHolder_.Add(other.idColorHolder_);
		_unknownFields = UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 8:
					MapId = input.ReadInt32();
					break;
				case 18:
					ropesData_.AddEntriesFrom(input, _repeated_ropesData_codec);
					break;
				case 26:
					goldenRopesData_.AddEntriesFrom(input, _repeated_goldenRopesData_codec);
					break;
				case 34:
					clampsData_.AddEntriesFrom(input, _repeated_clampsData_codec);
					break;
				case 42:
					keysData_.AddEntriesFrom(input, _repeated_keysData_codec);
					break;
				case 50:
					indexLockData_.AddEntriesFrom(input, _repeated_indexLockData_codec);
					break;
				case 58:
					columnsData_.AddEntriesFrom(input, _repeated_columnsData_codec);
					break;
				case 66:
					indexRemoveData_.AddEntriesFrom(input, _repeated_indexRemoveData_codec);
					break;
				case 74:
					idColorHolder_.AddEntriesFrom(input, _repeated_idColorHolder_codec);
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
					break;
			}
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 8:
					MapId = input.ReadInt32();
					break;
				case 18:
					ropesData_.AddEntriesFrom(ref input, _repeated_ropesData_codec);
					break;
				case 26:
					goldenRopesData_.AddEntriesFrom(ref input, _repeated_goldenRopesData_codec);
					break;
				case 34:
					clampsData_.AddEntriesFrom(ref input, _repeated_clampsData_codec);
					break;
				case 42:
					keysData_.AddEntriesFrom(ref input, _repeated_keysData_codec);
					break;
				case 50:
					indexLockData_.AddEntriesFrom(ref input, _repeated_indexLockData_codec);
					break;
				case 58:
					columnsData_.AddEntriesFrom(ref input, _repeated_columnsData_codec);
					break;
				case 66:
					indexRemoveData_.AddEntriesFrom(ref input, _repeated_indexRemoveData_codec);
					break;
				case 74:
					idColorHolder_.AddEntriesFrom(ref input, _repeated_idColorHolder_codec);
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
					break;
			}
		}
	}
}
