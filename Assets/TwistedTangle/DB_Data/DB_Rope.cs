using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Rope : IMessage<DB_Rope>, IMessage, IEquatable<DB_Rope>, IDeepCloneable<DB_Rope>, IBufferMessage
{
	private static readonly MessageParser<DB_Rope> _parser = new MessageParser<DB_Rope>(() => new DB_Rope());

	private UnknownFieldSet _unknownFields;

	public const int ColorIdFieldNumber = 1;
	private int colorId_;

	public const int RopeTypeFieldNumber = 2;
	private int ropeType_;

	public const int PinsDataFieldNumber = 3;
	private static readonly FieldCodec<DB_Pin> _repeated_pinsData_codec = FieldCodec.ForMessage(26, DB_Pin.Parser);
	private readonly RepeatedField<DB_Pin> pinsData_ = new RepeatedField<DB_Pin>();

	public const int ListParticlesFieldNumber = 4;
	private static readonly FieldCodec<DB_Particle> _repeated_listParticles_codec = FieldCodec.ForMessage(34, DB_Particle.Parser);
	private readonly RepeatedField<DB_Particle> listParticles_ = new RepeatedField<DB_Particle>();

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_Rope> Parser => _parser;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => Descriptor;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public int ColorId
	{
		get { return colorId_; }
		set { colorId_ = value; }
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public int RopeType
	{
		get { return ropeType_; }
		set { ropeType_ = value; }
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Pin> PinsData => pinsData_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public RepeatedField<DB_Particle> ListParticles => listParticles_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Rope()
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Rope(DB_Rope other) : this()
	{
		colorId_ = other.colorId_;
		ropeType_ = other.ropeType_;
		pinsData_ = new RepeatedField<DB_Pin>();
		pinsData_.AddRange(other.pinsData_);
		listParticles_ = new RepeatedField<DB_Particle>();
		listParticles_.AddRange(other.listParticles_);
		_unknownFields = other._unknownFields;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Rope Clone()
	{
		return new DB_Rope(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override bool Equals(object other)
	{
		return Equals(other as DB_Rope);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool Equals(DB_Rope other)
	{
		if (ReferenceEquals(other, null))
		{
			return false;
		}
		if (ReferenceEquals(other, this))
		{
			return true;
		}
		if (ColorId != other.ColorId)
		{
			return false;
		}
		if (RopeType != other.RopeType)
		{
			return false;
		}
		if (!pinsData_.Equals(other.pinsData_))
		{
			return false;
		}
		if (!listParticles_.Equals(other.listParticles_))
		{
			return false;
		}
		return Equals(_unknownFields, other._unknownFields);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override int GetHashCode()
	{
		int hash = 1;
		if (ColorId != 0)
		{
			hash ^= ColorId.GetHashCode();
		}
		if (RopeType != 0)
		{
			hash ^= RopeType.GetHashCode();
		}
		hash ^= pinsData_.GetHashCode();
		hash ^= listParticles_.GetHashCode();
		if (_unknownFields != null)
		{
			hash ^= _unknownFields.GetHashCode();
		}
		return hash;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return JsonFormatter.Default.Format(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
		if (ColorId != 0)
		{
			output.WriteRawTag(8);
			output.WriteInt32(ColorId);
		}
		if (RopeType != 0)
		{
			output.WriteRawTag(16);
			output.WriteInt32(RopeType);
		}
		pinsData_.WriteTo(output, _repeated_pinsData_codec);
		listParticles_.WriteTo(output, _repeated_listParticles_codec);
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
		if (ColorId != 0)
		{
			output.WriteRawTag(8);
			output.WriteInt32(ColorId);
		}
		if (RopeType != 0)
		{
			output.WriteRawTag(16);
			output.WriteInt32(RopeType);
		}
		pinsData_.WriteTo(ref output, _repeated_pinsData_codec);
		listParticles_.WriteTo(ref output, _repeated_listParticles_codec);
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(ref output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		int size = 0;
		if (ColorId != 0)
		{
			size += 1 + CodedOutputStream.ComputeInt32Size(ColorId);
		}
		if (RopeType != 0)
		{
			size += 1 + CodedOutputStream.ComputeInt32Size(RopeType);
		}
		size += pinsData_.CalculateSize(_repeated_pinsData_codec);
		size += listParticles_.CalculateSize(_repeated_listParticles_codec);
		if (_unknownFields != null)
		{
			size += _unknownFields.CalculateSize();
		}
		return size;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(DB_Rope other)
	{
		if (other == null)
		{
			return;
		}
		if (other.ColorId != 0)
		{
			ColorId = other.ColorId;
		}
		if (other.RopeType != 0)
		{
			RopeType = other.RopeType;
		}
		pinsData_.Add(other.pinsData_);
		listParticles_.Add(other.listParticles_);
		_unknownFields = UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 8:
					ColorId = input.ReadInt32();
					break;
				case 16:
					RopeType = input.ReadInt32();
					break;
				case 26:
					pinsData_.AddEntriesFrom(input, _repeated_pinsData_codec);
					break;
				case 34:
					listParticles_.AddEntriesFrom(input, _repeated_listParticles_codec);
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
					break;
			}
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 8:
					ColorId = input.ReadInt32();
					break;
				case 16:
					RopeType = input.ReadInt32();
					break;
				case 26:
					pinsData_.AddEntriesFrom(ref input, _repeated_pinsData_codec);
					break;
				case 34:
					listParticles_.AddEntriesFrom(ref input, _repeated_listParticles_codec);
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
					break;
			}
		}
	}
}
