using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Vector3float : IMessage<DB_Vector3float>, IMessage, IEquatable<DB_Vector3float>, IDeepCloneable<DB_Vector3float>, IBufferMessage
{
	private static readonly MessageParser<DB_Vector3float> _parser;

	private UnknownFieldSet _unknownFields;

	public const int XFieldNumber = 1;

	private float x_;

	public const int YFieldNumber = 2;

	private float y_;

	public const int ZFieldNumber = 3;

	private float z_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_Vector3float> Parser => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageDescriptor Descriptor => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	MessageDescriptor IMessage.Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public float X
	{
		get
		{
			return 0f;
		}
		set
		{
		}
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public float Y
	{
		get
		{
			return 0f;
		}
		set
		{
		}
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public float Z
	{
		get
		{
			return 0f;
		}
		set
		{
		}
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3float()
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3float(DB_Vector3float other)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3float Clone()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override bool Equals(object other)
	{
		return false;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool Equals(DB_Vector3float other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override int GetHashCode()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public int CalculateSize()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(DB_Vector3float other)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
