using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_ColumnData : IMessage<DB_ColumnData>, IMessage, IEquatable<DB_ColumnData>, IDeepCloneable<DB_ColumnData>, IBufferMessage
{
	private static readonly MessageParser<DB_ColumnData> _parser;

	private UnknownFieldSet _unknownFields;

	public const int PositionFieldNumber = 1;

	private DB_Vector3float position_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_ColumnData> Parser => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Vector3float Position
	{
		get
		{
			return null;
		}
		set
		{
		}
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_ColumnData()
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_ColumnData(DB_ColumnData other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_ColumnData Clone()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override bool Equals(object other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool Equals(DB_ColumnData other)
	{
		return false;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override int GetHashCode()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		return 0;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(DB_ColumnData other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
