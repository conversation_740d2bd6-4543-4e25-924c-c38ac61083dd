using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Pin : IMessage<DB_Pin>, IMessage, IEquatable<DB_Pin>, IDeepCloneable<DB_Pin>, IBufferMessage
{
	private static readonly MessageParser<DB_Pin> _parser = new MessageParser<DB_Pin>(() => new DB_Pin());

	private UnknownFieldSet _unknownFields;

	public const int PositionFieldNumber = 1;
	private DB_Vector3 position_;

	public const int IsLockedFieldNumber = 2;
	private bool isLocked_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<DB_Pin> Parser => _parser;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => Descriptor;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3 Position
	{
		get { return position_; }
		set { position_ = value; }
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool IsLocked
	{
		get { return isLocked_; }
		set { isLocked_ = value; }
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Pin()
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Pin(DB_Pin other) : this()
	{
		position_ = other.position_ != null ? other.position_.Clone() : null;
		isLocked_ = other.isLocked_;
		_unknownFields = other._unknownFields;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Pin Clone()
	{
		return new DB_Pin(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override bool Equals(object other)
	{
		return Equals(other as DB_Pin);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public bool Equals(DB_Pin other)
	{
		if (ReferenceEquals(other, null))
		{
			return false;
		}
		if (ReferenceEquals(other, this))
		{
			return true;
		}
		if (!object.Equals(Position, other.Position))
		{
			return false;
		}
		if (IsLocked != other.IsLocked)
		{
			return false;
		}
		return Equals(_unknownFields, other._unknownFields);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override int GetHashCode()
	{
		int hash = 1;
		if (position_ != null)
		{
			hash ^= Position.GetHashCode();
		}
		if (IsLocked != false)
		{
			hash ^= IsLocked.GetHashCode();
		}
		if (_unknownFields != null)
		{
			hash ^= _unknownFields.GetHashCode();
		}
		return hash;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return JsonFormatter.Default.Format(this);
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
		if (position_ != null)
		{
			output.WriteRawTag(10);
			output.WriteMessage(Position);
		}
		if (IsLocked != false)
		{
			output.WriteRawTag(16);
			output.WriteBool(IsLocked);
		}
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
		if (position_ != null)
		{
			output.WriteRawTag(10);
			output.WriteMessage(Position);
		}
		if (IsLocked != false)
		{
			output.WriteRawTag(16);
			output.WriteBool(IsLocked);
		}
		if (_unknownFields != null)
		{
			_unknownFields.WriteTo(ref output);
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		int size = 0;
		if (position_ != null)
		{
			size += 1 + CodedOutputStream.ComputeMessageSize(Position);
		}
		if (IsLocked != false)
		{
			size += 1 + 1;
		}
		if (_unknownFields != null)
		{
			size += _unknownFields.CalculateSize();
		}
		return size;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(DB_Pin other)
	{
		if (other == null)
		{
			return;
		}
		if (other.position_ != null)
		{
			if (position_ == null)
			{
				Position = new DB_Vector3();
			}
			Position.MergeFrom(other.Position);
		}
		if (other.IsLocked != false)
		{
			IsLocked = other.IsLocked;
		}
		_unknownFields = UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 10:
					if (position_ == null)
					{
						Position = new DB_Vector3();
					}
					input.ReadMessage(Position);
					break;
				case 16:
					IsLocked = input.ReadBool();
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
					break;
			}
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
		uint tag;
		while ((tag = input.ReadTag()) != 0)
		{
			switch (tag)
			{
				case 10:
					if (position_ == null)
					{
						Position = new DB_Vector3();
					}
					input.ReadMessage(Position);
					break;
				case 16:
					IsLocked = input.ReadBool();
					break;
				default:
					_unknownFields = UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
					break;
			}
		}
	}
}
