using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class DB_Particle : IMessage<DB_Particle>, IMessage, IEquatable<DB_Particle>, IDeepCloneable<DB_Particle>, IBufferMessage
{
	private static readonly MessageParser<DB_Particle> _parser;

	private UnknownFieldSet _unknownFields;

	public const int PositionFieldNumber = 1;

	private DB_Vector3float position_;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageParser<DB_Particle> Parser => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Vector3float Position
	{
		get
		{
			return null;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Particle()
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public DB_Particle(DB_Particle other)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public DB_Particle Clone()
	{
		return null;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override bool Equals(object other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool Equals(DB_Particle other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override int GetHashCode()
	{
		return 0;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public override string ToString()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int CalculateSize()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(DB_Particle other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
