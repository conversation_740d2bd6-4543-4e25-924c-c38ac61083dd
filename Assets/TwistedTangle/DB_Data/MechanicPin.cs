using System;
using System.CodeDom.Compiler;
using System.Diagnostics;
using Google.Protobuf;
using Google.Protobuf.Reflection;

[DebuggerDisplay("{ToString(),nq}")]
public sealed class MechanicPin : IMessage<MechanicPin>, IMessage, IEquatable<MechanicPin>, IDeepCloneable<MechanicPin>, IBufferMessage
{
	private static readonly MessageParser<MechanicPin> _parser;

	private UnknownFieldSet _unknownFields;

	public const int IsIceFieldNumber = 1;

	private bool isIce_;

	public const int IsBoomFieldNumber = 2;

	private bool isBoom_;

	public const int TimeBoomFieldNumber = 3;

	private int timeBoom_;

	public const int IceCountFieldNumber = 4;

	private int iceCount_;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public static MessageParser<MechanicPin> Parser => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public static MessageDescriptor Descriptor => null;

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	MessageDescriptor IMessage.Descriptor => null;

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool IsIce
	{
		get
		{
			return false;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool IsBoom
	{
		get
		{
			return false;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int TimeBoom
	{
		get
		{
			return 0;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public int IceCount
	{
		get
		{
			return 0;
		}
		set
		{
		}
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public MechanicPin()
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public MechanicPin(MechanicPin other)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public MechanicPin Clone()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override bool Equals(object other)
	{
		return false;
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	public bool Equals(MechanicPin other)
	{
		return false;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override int GetHashCode()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public override string ToString()
	{
		return null;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void WriteTo(CodedOutputStream output)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	void IBufferMessage.InternalWriteTo(ref WriteContext output)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public int CalculateSize()
	{
		return 0;
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(MechanicPin other)
	{
	}

	[DebuggerNonUserCode]
	[GeneratedCode("protoc", null)]
	public void MergeFrom(CodedInputStream input)
	{
	}

	[GeneratedCode("protoc", null)]
	[DebuggerNonUserCode]
	void IBufferMessage.InternalMergeFrom(ref ParseContext input)
	{
	}
}
