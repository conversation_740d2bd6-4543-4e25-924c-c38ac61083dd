using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using System.Collections.Generic;

public class BytesFileAnalyzer : EditorWindow
{
    private string filePath = "";
    private byte[] fileBytes;
    private Vector2 scrollPosition;
    private bool showPrimitiveValues = true;
    private bool showStringValues = true;
    private bool showVectorValues = true;
    
    [MenuItem("Tools/Bytes File Analyzer")]
    public static void ShowWindow()
    {
        GetWindow<BytesFileAnalyzer>("Bytes File Analyzer");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Bytes File Analyzer", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        filePath = EditorGUILayout.TextField("File Path:", filePath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.OpenFilePanel("Select Bytes File", "", "bytes");
            if (!string.IsNullOrEmpty(path))
            {
                filePath = path;
                LoadFile();
            }
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.Button("Analyze File"))
        {
            LoadFile();
        }
        
        showPrimitiveValues = EditorGUILayout.Toggle("Show Primitive Values", showPrimitiveValues);
        showStringValues = EditorGUILayout.Toggle("Show String Values", showStringValues);
        showVectorValues = EditorGUILayout.Toggle("Show Vector Values", showVectorValues);
        
        EditorGUILayout.Space();
        
        if (fileBytes != null && fileBytes.Length > 0)
        {
            EditorGUILayout.LabelField($"File Size: {fileBytes.Length} bytes");
            
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(400));
            
            // Hiển thị thông tin cơ bản về file
            EditorGUILayout.LabelField("File Analysis:", EditorStyles.boldLabel);
            
            // Phân tích các giá trị nguyên thủy
            if (showPrimitiveValues)
            {
                EditorGUILayout.LabelField("Primitive Values:", EditorStyles.boldLabel);
                AnalyzePrimitiveValues();
            }
            
            // Phân tích các chuỗi
            if (showStringValues)
            {
                EditorGUILayout.LabelField("String Values:", EditorStyles.boldLabel);
                AnalyzeStringValues();
            }
            
            // Phân tích các vector
            if (showVectorValues)
            {
                EditorGUILayout.LabelField("Vector Values:", EditorStyles.boldLabel);
                AnalyzeVectorValues();
            }
            
            EditorGUILayout.EndScrollView();
            
            if (GUILayout.Button("Export Analysis to Text File"))
            {
                ExportAnalysis();
            }
        }
    }
    
    private void LoadFile()
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid file", "OK");
            return;
        }
        
        try
        {
            fileBytes = File.ReadAllBytes(filePath);
            Debug.Log($"Loaded file: {filePath}, Size: {fileBytes.Length} bytes");
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Error", "Error loading file: " + ex.Message, "OK");
            Debug.LogError("Error loading file: " + ex.Message);
        }
    }
    
    private void AnalyzePrimitiveValues()
    {
        if (fileBytes == null || fileBytes.Length < 4)
            return;
            
        // Hiển thị các giá trị int ở các vị trí khác nhau
        for (int i = 0; i < fileBytes.Length - 3; i += 4)
        {
            int intValue = System.BitConverter.ToInt32(fileBytes, i);
            float floatValue = System.BitConverter.ToSingle(fileBytes, i);
            
            EditorGUILayout.LabelField($"Offset {i}: Int32 = {intValue}, Float = {floatValue}");
        }
    }
    
    private void AnalyzeStringValues()
    {
        if (fileBytes == null || fileBytes.Length < 4)
            return;
            
        // Tìm các chuỗi ASCII có thể đọc được
        List<string> foundStrings = new List<string>();
        StringBuilder currentString = new StringBuilder();
        
        for (int i = 0; i < fileBytes.Length; i++)
        {
            byte b = fileBytes[i];
            
            // Nếu là ký tự ASCII có thể đọc được
            if (b >= 32 && b <= 126)
            {
                currentString.Append((char)b);
            }
            else
            {
                // Kết thúc chuỗi hiện tại
                if (currentString.Length >= 4) // Chỉ lấy chuỗi có độ dài >= 4
                {
                    foundStrings.Add($"Offset {i - currentString.Length}: \"{currentString}\"");
                }
                currentString.Clear();
            }
        }
        
        // Hiển thị các chuỗi tìm được
        foreach (string str in foundStrings)
        {
            EditorGUILayout.LabelField(str);
        }
    }
    
    private void AnalyzeVectorValues()
    {
        if (fileBytes == null || fileBytes.Length < 12)
            return;
            
        // Tìm các giá trị có thể là Vector3
        for (int i = 0; i < fileBytes.Length - 11; i += 4)
        {
            float x = System.BitConverter.ToSingle(fileBytes, i);
            float y = System.BitConverter.ToSingle(fileBytes, i + 4);
            float z = System.BitConverter.ToSingle(fileBytes, i + 8);
            
            // Chỉ hiển thị các vector có giá trị hợp lý
            if (!float.IsNaN(x) && !float.IsNaN(y) && !float.IsNaN(z) &&
                !float.IsInfinity(x) && !float.IsInfinity(y) && !float.IsInfinity(z) &&
                (Mathf.Abs(x) < 1000 && Mathf.Abs(y) < 1000 && Mathf.Abs(z) < 1000))
            {
                EditorGUILayout.LabelField($"Offset {i}: Vector3({x}, {y}, {z})");
            }
        }
    }
    
    private void ExportAnalysis()
    {
        if (fileBytes == null)
            return;
            
        string outputPath = EditorUtility.SaveFilePanel("Save Analysis", Path.GetDirectoryName(filePath), 
            Path.GetFileNameWithoutExtension(filePath) + "_analysis", "txt");
            
        if (string.IsNullOrEmpty(outputPath))
            return;
            
        StringBuilder sb = new StringBuilder();
        
        sb.AppendLine($"Analysis of file: {filePath}");
        sb.AppendLine($"File Size: {fileBytes.Length} bytes");
        sb.AppendLine();
        
        // Thêm phân tích hex dump
        sb.AppendLine("Hex Dump:");
        int lineLength = 16;
        for (int i = 0; i < fileBytes.Length; i += lineLength)
        {
            sb.AppendFormat("{0:X8}: ", i);
            
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < fileBytes.Length)
                    sb.AppendFormat("{0:X2} ", fileBytes[i + j]);
                else
                    sb.Append("   ");
                
                if (j == 7)
                    sb.Append(" ");
            }
            
            sb.Append(" | ");
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < fileBytes.Length)
                {
                    char c = (char)fileBytes[i + j];
                    sb.Append(char.IsControl(c) ? '.' : c);
                }
            }
            
            sb.AppendLine();
        }
        
        File.WriteAllText(outputPath, sb.ToString());
        EditorUtility.DisplayDialog("Success", "Analysis exported to: " + outputPath, "OK");
    }
}