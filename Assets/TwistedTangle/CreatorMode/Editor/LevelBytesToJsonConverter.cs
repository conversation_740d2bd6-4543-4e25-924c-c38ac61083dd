using UnityEngine;
using UnityEditor;
using System.IO;
using Crescive.CreatorMode;

public class LevelBytesToJsonConverter : EditorWindow
{
    private string levelBytesPath = "";
    private string outputJsonPath = "";
    private string jsonResult = "";
    private Vector2 scrollPosition;

    [MenuItem("Tools/Level Bytes To JSON Converter")]
    public static void ShowWindow()
    {
        GetWindow<LevelBytesToJsonConverter>("Level Bytes Converter");
    }

    private void OnGUI()
    {
        GUILayout.Label("Level Bytes to JSON Converter", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        levelBytesPath = EditorGUILayout.TextField("Level Bytes Path:", levelBytesPath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.OpenFilePanel("Select Level Bytes File", "", "bytes");
            if (!string.IsNullOrEmpty(path))
            {
                levelBytesPath = path;
                outputJsonPath = Path.ChangeExtension(path, "json");
            }
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        outputJsonPath = EditorGUILayout.TextField("Output JSON Path:", outputJsonPath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.SaveFilePanel("Save JSON File", Path.GetDirectoryName(levelBytesPath), Path.GetFileNameWithoutExtension(levelBytesPath), "json");
            if (!string.IsNullOrEmpty(path))
            {
                outputJsonPath = path;
            }
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.Button("Convert"))
        {
            ConvertBytesToJson();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("JSON Result:");
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));
        EditorGUILayout.TextArea(jsonResult, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        if (!string.IsNullOrEmpty(jsonResult))
        {
            if (GUILayout.Button("Save JSON to File"))
            {
                File.WriteAllText(outputJsonPath, jsonResult);
                AssetDatabase.Refresh();
                EditorUtility.DisplayDialog("Success", "JSON saved to: " + outputJsonPath, "OK");
            }
            
            if (GUILayout.Button("Copy JSON to Clipboard"))
            {
                EditorGUIUtility.systemCopyBuffer = jsonResult;
                EditorUtility.DisplayDialog("Success", "JSON copied to clipboard", "OK");
            }
        }
    }
    
    private void ConvertBytesToJson()
    {
        if (string.IsNullOrEmpty(levelBytesPath) || !File.Exists(levelBytesPath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid level.bytes file", "OK");
            return;
        }
        
        try
        {
            // Đọc file bytes
            byte[] bytes = File.ReadAllBytes(levelBytesPath);
            
            // Phương pháp 1: Sử dụng JsonUtility
            LevelCreatorSaveData levelData = BytesToLevelData(bytes);
            
            if (levelData != null)
            {
                jsonResult = levelData.ToJson();
                Debug.Log("Conversion successful!");
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Failed to deserialize level data", "OK");
            }
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Error", "Error converting bytes to JSON: " + ex.Message, "OK");
            Debug.LogError("Error converting bytes to JSON: " + ex.Message);
        }
    }
    
    private LevelCreatorSaveData BytesToLevelData(byte[] bytes)
    {
        // Phương pháp 1: Thử sử dụng BinaryFormatter
        try
        {
            using (MemoryStream ms = new MemoryStream(bytes))
            {
                System.Runtime.Serialization.Formatters.Binary.BinaryFormatter bf = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                return (LevelCreatorSaveData)bf.Deserialize(ms);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning("BinaryFormatter method failed: " + ex.Message);
        }
        
        // Phương pháp 2: Thử sử dụng JsonUtility
        try
        {
            string json = System.Text.Encoding.UTF8.GetString(bytes);
            return LevelCreatorSaveData.FromJson(json);
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning("JsonUtility method failed: " + ex.Message);
        }
        
        // Phương pháp 3: Thử sử dụng Easy Save 3 nếu có
        try
        {
            if (typeof(ES3Reader) != null)
            {
                ES3Settings settings = new ES3Settings();
                ES3Reader reader = ES3Reader.Create(bytes, settings);
                if (reader != null)
                {
                    LevelCreatorSaveData data = new LevelCreatorSaveData();
                    reader.ReadInto(data);
                    reader.Dispose();
                    return data;
                }
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogWarning("Easy Save 3 method failed: " + ex.Message);
        }
        
        Debug.LogError("All deserialization methods failed");
        return null;
    }
}