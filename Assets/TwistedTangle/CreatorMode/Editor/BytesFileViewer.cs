using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using System.Collections.Generic;

public class BytesFileViewer : EditorWindow
{
    private string filePath = "";
    private string fileContent = "";
    private Vector2 scrollPosition;
    private ViewMode currentViewMode = ViewMode.Hex;
    private Encoding currentEncoding = Encoding.UTF8;
    
    private enum ViewMode
    {
        Hex,
        Text,
        Base64,
        Binary
    }
    
    private enum EncodingType
    {
        UTF8,
        ASCII,
        Unicode,
        UTF32
    }
    
    [MenuItem("Tools/Bytes File Viewer")]
    public static void ShowWindow()
    {
        GetWindow<BytesFileViewer>("Bytes File Viewer");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Bytes File Viewer", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        filePath = EditorGUILayout.TextField("File Path:", filePath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.OpenFilePanel("Select Bytes File", "", "bytes");
            if (!string.IsNullOrEmpty(path))
            {
                filePath = path;
                LoadFile();
            }
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("View Mode:", GUILayout.Width(80));
        ViewMode newViewMode = (ViewMode)EditorGUILayout.EnumPopup(currentViewMode);
        if (newViewMode != currentViewMode)
        {
            currentViewMode = newViewMode;
            LoadFile();
        }
        
        EditorGUILayout.LabelField("Encoding:", GUILayout.Width(80));
        EncodingType encodingType = (EncodingType)EditorGUILayout.EnumPopup(
            currentEncoding == Encoding.UTF8 ? EncodingType.UTF8 :
            currentEncoding == Encoding.ASCII ? EncodingType.ASCII :
            currentEncoding == Encoding.Unicode ? EncodingType.Unicode :
            EncodingType.UTF32);
            
        Encoding newEncoding = 
            encodingType == EncodingType.UTF8 ? Encoding.UTF8 :
            encodingType == EncodingType.ASCII ? Encoding.ASCII :
            encodingType == EncodingType.Unicode ? Encoding.Unicode :
            Encoding.UTF32;
            
        if (newEncoding != currentEncoding)
        {
            currentEncoding = newEncoding;
            LoadFile();
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.Button("Load File"))
        {
            LoadFile();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("File Content:");
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(400));
        EditorGUILayout.TextArea(fileContent, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        if (!string.IsNullOrEmpty(fileContent))
        {
            if (GUILayout.Button("Copy to Clipboard"))
            {
                EditorGUIUtility.systemCopyBuffer = fileContent;
                EditorUtility.DisplayDialog("Success", "Content copied to clipboard", "OK");
            }
            
            if (GUILayout.Button("Save to Text File"))
            {
                string outputPath = EditorUtility.SaveFilePanel("Save as Text", Path.GetDirectoryName(filePath), 
                    Path.GetFileNameWithoutExtension(filePath), "txt");
                
                if (!string.IsNullOrEmpty(outputPath))
                {
                    File.WriteAllText(outputPath, fileContent);
                    EditorUtility.DisplayDialog("Success", "Content saved to: " + outputPath, "OK");
                }
            }
        }
    }
    
    private void LoadFile()
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid file", "OK");
            return;
        }
        
        try
        {
            byte[] bytes = File.ReadAllBytes(filePath);
            
            switch (currentViewMode)
            {
                case ViewMode.Hex:
                    fileContent = BytesToHexString(bytes);
                    break;
                case ViewMode.Text:
                    fileContent = currentEncoding.GetString(bytes);
                    break;
                case ViewMode.Base64:
                    fileContent = System.Convert.ToBase64String(bytes);
                    break;
                case ViewMode.Binary:
                    fileContent = BytesToBinaryString(bytes);
                    break;
            }
            
            // Kiểm tra xem file có phải là file JSON không
            if (currentViewMode == ViewMode.Text && fileContent.Trim().StartsWith("{") && fileContent.Trim().EndsWith("}"))
            {
                EditorUtility.DisplayDialog("Info", "This appears to be a JSON file", "OK");
            }
            
            // Kiểm tra xem file có phải là file XML không
            if (currentViewMode == ViewMode.Text && fileContent.Trim().StartsWith("<") && fileContent.Trim().EndsWith(">"))
            {
                EditorUtility.DisplayDialog("Info", "This appears to be an XML file", "OK");
            }
            
            // Kiểm tra các định dạng file phổ biến
            DetectFileFormat(bytes);
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Error", "Error loading file: " + ex.Message, "OK");
            Debug.LogError("Error loading file: " + ex.Message);
        }
    }
    
    private string BytesToHexString(byte[] bytes)
    {
        StringBuilder sb = new StringBuilder();
        int lineLength = 16;
        
        for (int i = 0; i < bytes.Length; i += lineLength)
        {
            // Hiển thị offset
            sb.AppendFormat("{0:X8}: ", i);
            
            // Hiển thị hex bytes
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < bytes.Length)
                    sb.AppendFormat("{0:X2} ", bytes[i + j]);
                else
                    sb.Append("   ");
                
                // Thêm khoảng trắng giữa mỗi 8 bytes
                if (j == 7)
                    sb.Append(" ");
            }
            
            // Hiển thị ASCII
            sb.Append(" | ");
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < bytes.Length)
                {
                    char c = (char)bytes[i + j];
                    sb.Append(char.IsControl(c) ? '.' : c);
                }
            }
            
            sb.AppendLine();
        }
        
        return sb.ToString();
    }
    
    private string BytesToBinaryString(byte[] bytes)
    {
        StringBuilder sb = new StringBuilder();
        int lineLength = 8;
        
        for (int i = 0; i < bytes.Length; i += lineLength)
        {
            // Hiển thị offset
            sb.AppendFormat("{0:X8}: ", i);
            
            // Hiển thị binary bytes
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < bytes.Length)
                {
                    string binary = Convert.ToString(bytes[i + j], 2).PadLeft(8, '0');
                    sb.AppendFormat("{0} ", binary);
                }
                else
                {
                    sb.Append("         ");
                }
            }
            
            // Hiển thị ASCII
            sb.Append(" | ");
            for (int j = 0; j < lineLength; j++)
            {
                if (i + j < bytes.Length)
                {
                    char c = (char)bytes[i + j];
                    sb.Append(char.IsControl(c) ? '.' : c);
                }
            }
            
            sb.AppendLine();
        }
        
        return sb.ToString();
    }
    
    private void DetectFileFormat(byte[] bytes)
    {
        if (bytes.Length < 4)
            return;
            
        // Kiểm tra file signature (magic numbers)
        if (bytes[0] == 0x50 && bytes[1] == 0x4B && bytes[2] == 0x03 && bytes[3] == 0x04)
        {
            Debug.Log("This appears to be a ZIP file");
        }
        else if (bytes[0] == 0x1F && bytes[1] == 0x8B)
        {
            Debug.Log("This appears to be a GZIP file");
        }
        else if (bytes[0] == 0x42 && bytes[1] == 0x5A && bytes[2] == 0x68)
        {
            Debug.Log("This appears to be a BZIP2 file");
        }
        else if (bytes[0] == 0x7F && bytes[1] == 0x45 && bytes[2] == 0x4C && bytes[3] == 0x46)
        {
            Debug.Log("This appears to be an ELF file");
        }
        else if (bytes[0] == 0x25 && bytes[1] == 0x50 && bytes[2] == 0x44 && bytes[3] == 0x46)
        {
            Debug.Log("This appears to be a PDF file");
        }
        else if (bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47)
        {
            Debug.Log("This appears to be a PNG file");
        }
        else if (bytes[0] == 0xFF && bytes[1] == 0xD8 && bytes[2] == 0xFF)
        {
            Debug.Log("This appears to be a JPEG file");
        }
        else if (bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46 && bytes[3] == 0x38)
        {
            Debug.Log("This appears to be a GIF file");
        }
        else if (bytes[0] == 0x00 && bytes[1] == 0x01 && bytes[2] == 0x00 && bytes[3] == 0x00)
        {
            Debug.Log("This appears to be a Unity serialized file");
        }
        else if (bytes[0] == 0x73 && bytes[1] == 0x65 && bytes[2] == 0x72 && bytes[3] == 0x00)
        {
            Debug.Log("This appears to be a .NET BinaryFormatter serialized file");
        }
    }
}