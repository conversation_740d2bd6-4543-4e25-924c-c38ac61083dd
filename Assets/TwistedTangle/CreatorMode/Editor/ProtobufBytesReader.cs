using UnityEngine;
using UnityEditor;
using System.IO;
using Google.Protobuf;
using System.Text;

public class ProtobufBytesReader : EditorWindow
{
    private string filePath = "";
    private string resultText = "";
    private Vector2 scrollPosition;
    
    [MenuItem("Tools/Protobuf Bytes Reader")]
    public static void ShowWindow()
    {
        GetWindow<ProtobufBytesReader>("Protobuf Bytes Reader");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Protobuf Bytes Reader", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        filePath = EditorGUILayout.TextField("File Path:", filePath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.OpenFilePanel("Select Bytes File", "", "bytes");
            if (!string.IsNullOrEmpty(path))
            {
                filePath = path;
            }
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.But<PERSON>("Read Protobuf File"))
        {
            ReadProtobufFile();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Result:");
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(400));
        EditorGUILayout.TextArea(resultText, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        if (!string.IsNullOrEmpty(resultText))
        {
            if (GUILayout.Button("Copy to Clipboard"))
            {
                EditorGUIUtility.systemCopyBuffer = resultText;
                EditorUtility.DisplayDialog("Success", "Result copied to clipboard", "OK");
            }
            
            if (GUILayout.Button("Save to Text File"))
            {
                string outputPath = EditorUtility.SaveFilePanel("Save Result", Path.GetDirectoryName(filePath), 
                    Path.GetFileNameWithoutExtension(filePath) + "_result", "txt");
                
                if (!string.IsNullOrEmpty(outputPath))
                {
                    File.WriteAllText(outputPath, resultText);
                    EditorUtility.DisplayDialog("Success", "Result saved to: " + outputPath, "OK");
                }
            }
            
            if (GUILayout.Button("Convert to LevelCreatorSaveData"))
            {
                ConvertToLevelCreatorSaveData();
            }
        }
    }
    
    private void ReadProtobufFile()
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid file", "OK");
            return;
        }
        
        try
        {
            byte[] bytes = File.ReadAllBytes(filePath);
            
            // Sử dụng Parser của DB_Level để parse bytes
            DB_Level level = DB_Level.Parser.ParseFrom(bytes);
            
            if (level != null)
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine("Successfully parsed DB_Level:");
                sb.AppendLine($"MapId: {level.MapId}");
                sb.AppendLine($"RopesData count: {level.RopesData.Count}");
                sb.AppendLine($"GoldenRopesData count: {level.GoldenRopesData.Count}");
                sb.AppendLine($"ClampsData count: {level.ClampsData.Count}");
                sb.AppendLine($"KeysData count: {level.KeysData.Count}");
                sb.AppendLine($"IndexLockData count: {level.IndexLockData.Count}");
                sb.AppendLine($"ColumnsData count: {level.ColumnsData.Count}");
                sb.AppendLine($"IndexRemoveData count: {level.IndexRemoveData.Count}");
                sb.AppendLine($"IdColorHolder count: {level.IdColorHolder.Count}");
                
                // Hiển thị chi tiết về RopesData
                if (level.RopesData.Count > 0)
                {
                    sb.AppendLine("\nRopes Data:");
                    for (int i = 0; i < level.RopesData.Count; i++)
                    {
                        var rope = level.RopesData[i];
                        sb.AppendLine($"  Rope {i}:");
                        sb.AppendLine($"    ColorId: {rope.ColorId}");
                        sb.AppendLine($"    RopeType: {rope.RopeType}");
                        sb.AppendLine($"    PinsData count: {rope.PinsData.Count}");
                        sb.AppendLine($"    ListParticles count: {rope.ListParticles.Count}");
                    }
                }
                
                resultText = sb.ToString();
            }
            else
            {
                resultText = "Failed to parse DB_Level from bytes";
            }
        }
        catch (System.Exception ex)
        {
            resultText = "Error: " + ex.Message + "\n\nStack Trace:\n" + ex.StackTrace;
            Debug.LogError("Error reading Protobuf file: " + ex.Message);
        }
    }
    
    private void ConvertToLevelCreatorSaveData()
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid file", "OK");
            return;
        }
        
        try
        {
            byte[] bytes = File.ReadAllBytes(filePath);
            
            // Parse DB_Level từ bytes
            DB_Level level = DB_Level.Parser.ParseFrom(bytes);
            
            if (level != null)
            {
                // Tạo LevelCreatorSaveData từ DB_Level
                Crescive.CreatorMode.LevelCreatorSaveData saveData = new Crescive.CreatorMode.LevelCreatorSaveData();
                
                // Chuyển đổi dữ liệu
                saveData.MapId = level.MapId;
                
                // Chuyển đổi RopesData
                foreach (var dbRope in level.RopesData)
                {
                    Crescive.CreatorMode.RopeData ropeData = new Crescive.CreatorMode.RopeData();
                    // Thiết lập các thuộc tính của ropeData từ dbRope
                    // ...
                    saveData.ropesData.Add(ropeData);
                }
                
                // Chuyển đổi các dữ liệu khác
                // ...
                
                // Lưu LevelCreatorSaveData thành JSON
                string json = saveData.ToJson();
                
                // Lưu JSON vào file
                string outputPath = EditorUtility.SaveFilePanel("Save as JSON", Path.GetDirectoryName(filePath), 
                    Path.GetFileNameWithoutExtension(filePath), "json");
                
                if (!string.IsNullOrEmpty(outputPath))
                {
                    File.WriteAllText(outputPath, json);
                    EditorUtility.DisplayDialog("Success", "Converted and saved to: " + outputPath, "OK");
                }
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Failed to parse DB_Level from bytes", "OK");
            }
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Error", "Error converting to LevelCreatorSaveData: " + ex.Message, "OK");
            Debug.LogError("Error converting to LevelCreatorSaveData: " + ex.Message);
        }
    }
}