using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using System.Collections.Generic;
using Crescive.CreatorMode;

public class BytesFileReader : EditorWindow
{
    private string filePath = "";
    private string resultText = "";
    private Vector2 scrollPosition;
    private int selectedMethod = 0;
    
    private string[] methods = new string[]
    {
        "BinaryFormatter",
        "UTF8 Text",
        "ASCII Text",
        "Unicode Text",
        "Easy Save 3",
        "Unity JsonUtility",
        "Newtonsoft Json",
        "Protobuf (if available)",
        "MessagePack (if available)",
        "BSON (if available)"
    };
    
    [MenuItem("Tools/Bytes File Reader")]
    public static void ShowWindow()
    {
        GetWindow<BytesFileReader>("Bytes File Reader");
    }
    
    private void OnGUI()
    {
        GUILayout.Label("Bytes File Reader", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        filePath = EditorGUILayout.TextField("File Path:", filePath);
        if (GUILayout.Button("Browse", GUILayout.Width(100)))
        {
            string path = EditorUtility.OpenFilePanel("Select Bytes File", "", "bytes");
            if (!string.IsNullOrEmpty(path))
            {
                filePath = path;
            }
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.LabelField("Deserialization Method:");
        selectedMethod = EditorGUILayout.Popup(selectedMethod, methods);
        
        if (GUILayout.Button("Read File"))
        {
            ReadFile();
        }
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Result:");
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(400));
        EditorGUILayout.TextArea(resultText, GUILayout.ExpandHeight(true));
        EditorGUILayout.EndScrollView();
        
        if (!string.IsNullOrEmpty(resultText))
        {
            if (GUILayout.Button("Copy to Clipboard"))
            {
                EditorGUIUtility.systemCopyBuffer = resultText;
                EditorUtility.DisplayDialog("Success", "Result copied to clipboard", "OK");
            }
            
            if (GUILayout.Button("Save to Text File"))
            {
                string outputPath = EditorUtility.SaveFilePanel("Save Result", Path.GetDirectoryName(filePath), 
                    Path.GetFileNameWithoutExtension(filePath) + "_result", "txt");
                
                if (!string.IsNullOrEmpty(outputPath))
                {
                    File.WriteAllText(outputPath, resultText);
                    EditorUtility.DisplayDialog("Success", "Result saved to: " + outputPath, "OK");
                }
            }
        }
    }
    
    private void ReadFile()
    {
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            EditorUtility.DisplayDialog("Error", "Please select a valid file", "OK");
            return;
        }
        
        try
        {
            byte[] bytes = File.ReadAllBytes(filePath);
            
            switch (selectedMethod)
            {
                case 0: // BinaryFormatter
                    ReadWithBinaryFormatter(bytes);
                    break;
                case 1: // UTF8 Text
                    resultText = Encoding.UTF8.GetString(bytes);
                    break;
                case 2: // ASCII Text
                    resultText = Encoding.ASCII.GetString(bytes);
                    break;
                case 3: // Unicode Text
                    resultText = Encoding.Unicode.GetString(bytes);
                    break;
                case 4: // Easy Save 3
                    ReadWithEasySave3(bytes);
                    break;
                case 5: // Unity JsonUtility
                    ReadWithJsonUtility(bytes);
                    break;
                case 6: // Newtonsoft Json
                    ReadWithNewtonsoftJson(bytes);
                    break;
                case 7: // Protobuf
                    resultText = "Protobuf deserialization not implemented";
                    break;
                case 8: // MessagePack
                    resultText = "MessagePack deserialization not implemented";
                    break;
                case 9: // BSON
                    resultText = "BSON deserialization not implemented";
                    break;
            }
        }
        catch (System.Exception ex)
        {
            resultText = "Error: " + ex.Message + "\n\nStack Trace:\n" + ex.StackTrace;
            Debug.LogError("Error reading file: " + ex.Message);
        }
    }
    
    private void ReadWithBinaryFormatter(byte[] bytes)
    {
        try
        {
            using (MemoryStream ms = new MemoryStream(bytes))
            {
                System.Runtime.Serialization.Formatters.Binary.BinaryFormatter bf = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                object obj = bf.Deserialize(ms);
                
                if (obj is LevelCreatorSaveData levelData)
                {
                    resultText = levelData.ToJson();
                }
                else
                {
                    resultText = "Deserialized object is not a LevelCreatorSaveData. Type: " + obj.GetType().FullName;
                    
                    // Thử hiển thị thông tin về đối tượng
                    resultText += "\n\nObject ToString(): " + obj.ToString();
                }
            }
        }
        catch (System.Exception ex)
        {
            resultText = "BinaryFormatter failed: " + ex.Message;
        }
    }
    
    private void ReadWithEasySave3(byte[] bytes)
    {
        try
        {
            ES3Settings settings = new ES3Settings();
            ES3Reader reader = ES3Reader.Create(bytes, settings);
            
            if (reader != null)
            {
                LevelCreatorSaveData data = new LevelCreatorSaveData();
                reader.ReadInto(data);
                reader.Dispose();
                
                resultText = data.ToJson();
            }
            else
            {
                resultText = "ES3Reader.Create returned null";
            }
        }
        catch (System.Exception ex)
        {
            resultText = "Easy Save 3 failed: " + ex.Message;
        }
    }
    
    private void ReadWithJsonUtility(byte[] bytes)
    {
        try
        {
            string json = Encoding.UTF8.GetString(bytes);
            LevelCreatorSaveData data = JsonUtility.FromJson<LevelCreatorSaveData>(json);
            
            if (data != null)
            {
                resultText = JsonUtility.ToJson(data, true);
            }
            else
            {
                resultText = "JsonUtility.FromJson returned null";
            }
        }
        catch (System.Exception ex)
        {
            resultText = "JsonUtility failed: " + ex.Message;
        }
    }
    
    private void ReadWithNewtonsoftJson(byte[] bytes)
    {
        try
        {
            string json = Encoding.UTF8.GetString(bytes);
            LevelCreatorSaveData data = Newtonsoft.Json.JsonConvert.DeserializeObject<LevelCreatorSaveData>(json);
            
            if (data != null)
            {
                resultText = Newtonsoft.Json.JsonConvert.SerializeObject(data, Newtonsoft.Json.Formatting.Indented);
            }
            else
            {
                resultText = "JsonConvert.DeserializeObject returned null";
            }
        }
        catch (System.Exception ex)
        {
            resultText = "Newtonsoft Json failed: " + ex.Message;
        }
    }
}