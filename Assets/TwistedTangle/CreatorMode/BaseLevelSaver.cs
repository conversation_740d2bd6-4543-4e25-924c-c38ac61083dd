using UnityEngine;
using UnityEngine.Events;

namespace Crescive.CreatorMode
{
	public abstract class BaseLevelSaver<TCreatorData, TSaveData> : BaseSaver where TCreatorData : BaseLevelCreatorData<TSaveData>
	{
		[Range(0f, 1f)]
		[SerializeField]
		[Header("Settings")]
		[Space(5f)]
		private float screenshotResolutionScale;

		public UnityEvent OnSaveLevelStarted;

		public abstract TSaveData GetLevelSaveData();
	}
}
