using System.Runtime.CompilerServices;
using UnityEngine;
using UnityEngine.Events;

namespace Crescive.CreatorMode
{
	[CreateAssetMenu(fileName = "Map Profile Data", menuName = "Level Creator/Map Profile Data")]
	public class MapProfileData : ScriptableObject
	{
		public UnityEvent<int> OnMapNumberChanged;

		public int MapNumber
		{
			[CompilerGenerated]
			get
			{
				return 0;
			}
			[CompilerGenerated]
			private set
			{
			}
		}

		public int MinNumber
		{
			[CompilerGenerated]
			get
			{
				return 0;
			}
			[CompilerGenerated]
			private set
			{
			}
		}

		public int MaxNumber
		{
			[CompilerGenerated]
			get
			{
				return 0;
			}
			[CompilerGenerated]
			private set
			{
			}
		}

		public void SetMapNumber(int value)
		{
		}

		public void IncreaseMapNumber()
		{
		}

		public void DecreaseMapNumber()
		{
		}
	}
}
