using System;
using System.Collections.Generic;
using UnityEngine;

namespace Crescive.CreatorMode
{
    [Serializable]
    public class MinimalLevelData
    {
        // Chỉ định nghĩa các field bạn quan tâm
        public int MapId;
        public string Difficulty;
        public bool HasMoveCount;
        public int MoveCount;
        
        // Thêm các field khác nếu cần
        
        // Phương thức để chuyển đổi từ LevelCreatorSaveData đầy đủ
        public static MinimalLevelData FromFullData(LevelCreatorSaveData fullData)
        {
            if (fullData == null)
                return null;
                
            return new MinimalLevelData
            {
                MapId = fullData.MapId,
                Difficulty = fullData.Difficulty,
                HasMoveCount = fullData.HasMoveCount,
                MoveCount = fullData.MoveCount
            };
        }
        
        // Phương thức để chuyển đổi thành JSON
        public string ToJson()
        {
            return JsonUtility.ToJson(this, true);
        }
        
        // Phương thức để đọc từ file bytes
        public static MinimalLevelData FromBytes(byte[] bytes)
        {
            try
            {
                // Thử với BinaryFormatter
                using (var ms = new System.IO.MemoryStream(bytes))
                {
                    var bf = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                    var obj = bf.Deserialize(ms);
                    
                    if (obj is LevelCreatorSaveData fullData)
                        return FromFullData(fullData);
                    
                    if (obj is MinimalLevelData minData)
                        return minData;
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning("BinaryFormatter failed: " + ex.Message);
            }
            
            try
            {
                // Thử với JSON
                string json = System.Text.Encoding.UTF8.GetString(bytes);
                return JsonUtility.FromJson<MinimalLevelData>(json);
            }
            catch (Exception ex)
            {
                Debug.LogWarning("JSON parsing failed: " + ex.Message);
            }
            
            return null;
        }
    }
}