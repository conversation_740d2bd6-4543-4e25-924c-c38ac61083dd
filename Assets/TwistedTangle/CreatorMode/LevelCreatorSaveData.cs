using System;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Newtonsoft.Json;

namespace Crescive.CreatorMode
{
	[Serializable]
	public class LevelCreatorSaveData : BaseCreatorSaveData
	{
		public List<PinData> pinsData;

		public List<RopeData> ropesData;

		public List<ColumnData> columnsData;

		public List<ClampData> clampsData;

		public List<KeyData> keysData;

		public List<LockSlotData> lockSlotsData;

		public List<GoldenRopeData> goldenRopesData;

		public List<RopeWidthData> ropeWidthsData;

		public bool useNearColors;

		public bool useCustomNearColors;

		public List<Color> customNearColors;

		public bool HasMoveCount;

		public int MoveCount;

		public bool HasTimeLimit;

		public int TimeLimit;

		public int MapId;

		public string Difficulty;

		public LevelCreatorSaveData()
		{
			pinsData = new List<PinData>();
			ropesData = new List<RopeData>();
			columnsData = new List<ColumnData>();
			clampsData = new List<ClampData>();
			keysData = new List<KeyData>();
			lockSlotsData = new List<LockSlotData>();
			goldenRopesData = new List<GoldenRopeData>();
			ropeWidthsData = new List<RopeWidthData>();
			customNearColors = new List<Color>();
		}

		public LevelCreatorSaveData(LevelCreatorSaveData copy)
		{
			pinsData = new List<PinData>(copy.pinsData);
			ropesData = new List<RopeData>(copy.ropesData);
			columnsData = new List<ColumnData>(copy.columnsData);
			clampsData = new List<ClampData>(copy.clampsData);
			keysData = new List<KeyData>(copy.keysData);
			lockSlotsData = new List<LockSlotData>(copy.lockSlotsData);
			goldenRopesData = new List<GoldenRopeData>(copy.goldenRopesData);
			ropeWidthsData = new List<RopeWidthData>(copy.ropeWidthsData);
			useNearColors = copy.useNearColors;
			useCustomNearColors = copy.useCustomNearColors;
			customNearColors = new List<Color>(copy.customNearColors);
			HasMoveCount = copy.HasMoveCount;
			MoveCount = copy.MoveCount;
			HasTimeLimit = copy.HasTimeLimit;
			TimeLimit = copy.TimeLimit;
			MapId = copy.MapId;
			Difficulty = copy.Difficulty;
		}

		public override string ToJson()
		{
			return JsonConvert.SerializeObject(this, Formatting.Indented);
		}

		public static LevelCreatorSaveData FromJson(string json)
		{
			return JsonConvert.DeserializeObject<LevelCreatorSaveData>(json);
		}

		public override object Clone()
		{
			return new LevelCreatorSaveData(this);
		}
	}
}
