using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace Crescive.CreatorMode
{
	public abstract class BaseLevelCreator<TCreatorData, TSaveData> : BaseCreator where TCreatorData : BaseLevelCreatorData<TSaveData> where TSaveData : BaseCreatorSaveData
	{
		[SerializeField]
		private List<BaseCreatorSaveDataProcessor<TSaveData>> saveDataProcessors;

		public UnityEvent OnLevelCreationStarted;

		public UnityEvent OnLevelCreated;

		private TSaveData currentSaveData;

		public bool IsCreatingLevel => false;

		public abstract void ResetLevel();

		protected abstract IEnumerator CreateLevelInternal(TSaveData creatorData);

		private void PreProcessSaveData(TSaveData saveData)
		{
		}
	}
}
