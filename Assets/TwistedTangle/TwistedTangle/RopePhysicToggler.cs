using System.Runtime.CompilerServices;
using UnityEngine;

namespace Crescive.TwistedTangle
{
	public class RopePhysicToggler : MonoBehaviour
	{
		public bool IsActive => false;

		public float Timer
		{
			[CompilerGenerated]
			get
			{
				return 0f;
			}
			[CompilerGenerated]
			private set
			{
			}
		}

		public void SetPhysicsActive(bool active)
		{
		}

		public void SetTimer(float timer)
		{
		}

		public bool HasTimeOut(float resetPeriod)
		{
			return false;
		}
	}
}
