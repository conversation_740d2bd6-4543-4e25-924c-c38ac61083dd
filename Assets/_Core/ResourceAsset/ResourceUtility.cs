using UnityEngine;

namespace OnePuz
{
    public static class ResourceUtility
    {
        public static void LoadAsync<T>(string path, System.Action<T> onCompleted) where T : Object
        {
            var request = Resources.LoadAsync<T>(path);
            request.completed += _ => onCompleted?.Invoke((T)request.asset);
            request.completed += _ => Resources.UnloadUnusedAssets();
        }

        public static void Load<T>(string path, System.Action<T> action) where T : Object
        {
            action.Invoke(Resources.Load<T>(path));
            Resources.UnloadUnusedAssets();
        }
    }
}
