using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Utilities;
using PrimeTween;
using UnityEngine;

namespace OnePuz.UI
{
    public interface IRewardTarget
    {
        Transform GetRewardTargetTransform();
        void OnRewardReachedTarget(int rewardAmount);
        void OnLastRewardReachedTarget();
        void Appear();
    }

    public interface IContainRewardTarget
    {
        IRewardTarget GetRewardTarget<T>(T type) where T : System.Enum;
    }

    public class UIObtainRewardPanel : UIBasePanel
    {
        [Header("Particle Attractors")]
        [SerializeField] private ParticleAttractor _coinParticle;

        [SerializeField] private ParticleAttractor _gemParticle;

        [Header("Rewards")]
        [SerializeField] private Transform _rewardContainer;

        [SerializeField] private GameObject _rewardItemPrefab;

        private readonly Dictionary<CurrencyType, IRewardTarget> _currencyTargets = new Dictionary<CurrencyType, IRewardTarget>();
        private readonly Dictionary<BoosterType, IRewardTarget> _boosterTargets = new Dictionary<BoosterType, IRewardTarget>();
        private readonly List<UIRewardItem> _spawnedRewardItems = new();

        public override void Init(string id)
        {
            base.Init(id);

            Core.GlobalPool.WarmupCapacity(_rewardItemPrefab, 3);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        public async UniTask ObtainRewardsAsync(List<RewardData> rewards, params IContainRewardTarget[] targetPanels)
        {
            foreach (var rewardItem in _spawnedRewardItems)
            {
                rewardItem.CachedRectTransform.SetParent(_rewardContainer);
            }

            ResetProperties();

            // Claim rewards but don't update UI yet
            DataShortcut.ClaimRewards(rewards, false);

            // Find targets for all rewards
            foreach (var panel in targetPanels)
            {
                if (panel is not IPanel { pIsOpen: true })
                    continue;

                foreach (var rewardData in rewards)
                {
                    var rewardTarget = rewardData.RewardType == RewardType.Currency
                        ? panel.GetRewardTarget(rewardData.CurrencyType)
                        : panel.GetRewardTarget(rewardData.BoosterType);
                    
                    if (rewardTarget == null)
                        continue;

                    if (rewardData.RewardType == RewardType.Currency)
                        _currencyTargets.TryAdd(rewardData.CurrencyType, rewardTarget);
                    else
                        _boosterTargets.TryAdd(rewardData.BoosterType, rewardTarget);
                }
            }

            for (var i = 0; i < _spawnedRewardItems.Count; i++)
            {
                var rewardItem = _spawnedRewardItems[i];
                var rewardData = rewardItem.Data;

                // Get the target for this reward
                IRewardTarget target = null;
                if (rewardData.RewardType == RewardType.Currency)
                    _currencyTargets.TryGetValue(rewardData.CurrencyType, out target);
                else
                    _boosterTargets.TryGetValue(rewardData.BoosterType, out target);
                
                if (target == null)
                    continue;

                // Make target appear
                target.Appear();

                switch (rewardData.RewardType)
                {
                    // Determine which method to use based on reward type
                    case RewardType.Currency when rewardData.CurrencyType == CurrencyType.Coin && _coinParticle != null:
                    {
                        Tween.Scale(rewardItem.CachedRectTransform, Vector3.zero, 0.25f, Ease.InBack).OnComplete(() =>
                        {
                            var quantity = Mathf.Clamp(rewardData.Quantity, 1, 10);
                            _coinParticle.transform.position = rewardItem.CachedRectTransform.position;
                            _coinParticle.Play(target.GetRewardTargetTransform(), quantity, UIShortcut.Instance.GetUICamera());
                            _coinParticle.OnParticleReachedTarget.AddListener(() => { target.OnRewardReachedTarget(rewardData.Quantity); });
                            _coinParticle.OnLastParticleReachedTarget.AddListener(() => { target.OnLastRewardReachedTarget(); });
                        });
                        break;
                    }
                    case RewardType.Currency when rewardData.CurrencyType == CurrencyType.Gem && _gemParticle != null:
                    {
                        Tween.Scale(rewardItem.CachedRectTransform, Vector3.zero, 0.25f, Ease.InBack).OnComplete(() =>
                        {
                            var quantity = Mathf.Clamp(rewardData.Quantity, 1, 10);
                            _gemParticle.transform.position = rewardItem.CachedRectTransform.position;
                            _gemParticle.Play(target.GetRewardTargetTransform(), quantity, UIShortcut.Instance.GetUICamera());
                            _gemParticle.OnParticleReachedTarget.AddListener(() => { target.OnRewardReachedTarget(rewardData.Quantity); });
                            _gemParticle.OnLastParticleReachedTarget.AddListener(() => { target.OnLastRewardReachedTarget(); });
                        });
                        break;
                    }
                    case RewardType.Booster:
                    case RewardType.Skin:
                    case RewardType.InfiniteLive:
                    case RewardType.NoAds:
                    default:
                    {
                        if (rewardItem != null)
                        {
                            var attractor = rewardItem.Attractor;
                            if (attractor != null)
                            {
                                attractor.Setup(rewardItem.CachedRectTransform, target.GetRewardTargetTransform());
                                attractor.OnReachedTarget.AddListener(() =>
                                {
                                    target.OnRewardReachedTarget(rewardData.Quantity);
                                    target.OnLastRewardReachedTarget();
                                    RecycleRewardItem(rewardItem);
                                });

                                attractor.AnimateToTargetAsync(target.GetRewardTargetTransform(), 1f).Forget();
                            }
                            else
                            {
                                // If no attractor available, just destroy the reward item
                                RecycleRewardItem(rewardItem);
                            }
                        }

                        break;
                    }
                }

                await UniTask.Delay(200, cancellationToken: _cancellationToken);
            }

            // Wait for all particles to finish
            await UniTask.WaitUntil(() =>
                    _coinParticle?.IsPlaying == false,
                cancellationToken: _cancellationToken);

            ResetProperties();
            ClearRewardItems();
        }

        public UIRewardItem SpawnRewardItem(RewardData rewardData, Transform container)
        {
            var rewardItem = Core.GlobalPool.Spawn(_rewardItemPrefab, container).GetComponent<UIRewardItem>();

            rewardItem.Setup(rewardData);
            _spawnedRewardItems.Add(rewardItem);
            return rewardItem;
        }

        private void RecycleRewardItem(UIRewardItem rewardItem)
        {
            Core.GlobalPool.Recycle(rewardItem.gameObject);
        }

        private void ResetProperties()
        {
            _currencyTargets.Clear();
            _boosterTargets.Clear();
        }

        private void ClearRewardItems()
        {
            foreach (var item in _spawnedRewardItems.Where(item => item != null))
            {
                Core.GlobalPool.Recycle(item.gameObject);
            }

            _spawnedRewardItems.Clear();
        }
    }
}