using System;
using _FeatureHub.Utilities;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace _FeatureHub.UI.Runtime
{
    // ReSharper disable MemberCanBePrivate.Global
    public class ScrollRectExtender : ScrollRect
    {
        public bool isDraggable = true;
        public bool dragging { get; private set; }

        private readonly CallbackWrapper m_OnDragBeginListener = new();
        private readonly CallbackWrapper m_OnDragEndListener = new();
        private readonly CallbackWrapper m_OnDragListener = new();

        public void RegisterOnDragBegin<T>(T target, Action<T> @event) where T : class => m_OnDragBeginListener.Add(target, @event);
        public void RegisterOnDragEnd<T>(T target, Action<T> @event) where T : class  => m_OnDragEndListener.Add(target, @event);
        public void RegisterOnDrag<T>(T target, Action<T> @event) where T : class  => m_OnDragListener.Add(target, @event);
        
        public override void OnInitializePotentialDrag(PointerEventData eventData)
        {
            if(!isDraggable)
                return;
            base.OnInitializePotentialDrag(eventData);
        }

        public override void OnDrag(PointerEventData eventData)
        {
            if(!isDraggable || !dragging || eventData.button != PointerEventData.InputButton.Left)
                return;
            base.OnDrag(eventData);
            m_OnDragListener.Execute();
        }

        public override void OnBeginDrag(PointerEventData eventData)
        {
            if(!isDraggable)
                return;
            base.OnBeginDrag(eventData);
            m_OnDragBeginListener.Execute();
            dragging = true;
        }

        public override void OnEndDrag(PointerEventData eventData)
        {
            if(!isDraggable)
                return;
            base.OnEndDrag(eventData);
            m_OnDragEndListener.Execute();
            dragging = false;
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            dragging = false;
        }
    }
}