// using System;
// using _FeatureHub.Utilities;
// using Sirenix.OdinInspector;
// using UnityEngine;
// using UnityEngine.UI;
//
// namespace _FeatureHub.UI.Runtime
// {
//     public class ToggleSwapper : MonoBehaviour
//     {
//         [HorizontalGroup("group"), ListDrawerSettings(DefaultExpandedState = true, ShowItemCount = false), PreviewField]
//         public Image[] graphics;
//         [HorizontalGroup("group"), ListDrawerSettings(DefaultExpandedState = true, ShowItemCount = false), PreviewField]
//         public Sprite[] activeSprites;
//         [HorizontalGroup("group"), ListDrawerSettings(DefaultExpandedState = true, ShowItemCount = false), PreviewField]
//         public Sprite[] disableSprites;
//
//         private readonly CallbackWrapper<bool> m_OnSwapListener = new();
//
//         public Toggle Awakening()
//         {
//             var toggle = GetComponent<Toggle>();
//             if (toggle != null)
//                 toggle.onValueChanged.AddListener(HandleOnSwapped);
//             return toggle;
//         }
//
//         public void OnSwapped<T>(T target, Action<T, bool> onSwapped) where T : class
//         {
//             m_OnSwapListener.Add(target, onSwapped);
//         }
//
//         private void HandleOnSwapped(bool value)
//         {
//             if (value)
//             {
//                 for (var i = 0; i < graphics.Length; i++)
//                     graphics[i].sprite = activeSprites[i];
//             }
//             else
//             {
//                 for (var i = 0; i < graphics.Length; i++)
//                     graphics[i].sprite = disableSprites[i];
//             }
//
//             m_OnSwapListener.Execute(value);
//         }
//     }
// }