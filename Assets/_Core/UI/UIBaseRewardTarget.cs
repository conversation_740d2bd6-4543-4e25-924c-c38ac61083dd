using AssetKits.ParticleImage;
using OnePuz.Attributes;
using OnePuz.Audio;
using OnePuz.Extensions;
using UnityEngine;

namespace OnePuz.UI
{
    public class UIBaseRewardTarget : MonoBehaviour, IRewardTarget
    {
        [SerializeField, PreAssigned()]
        private Animator _animator;

        [SerializeField]
        private AnimationClip _collectedAnimation;

        [SerializeField]
        private ParticleImage _collectVFX;
        
        public Transform GetRewardTargetTransform()
        {
            return transform;
        }

        public void OnRewardReachedTarget(int rewardAmount)
        {
            AudioShortcut.PlayCollectItem();
            
            _animator.PlayManual(Animator.StringToHash(_collectedAnimation.name));
            
            _collectVFX?.Play();
        }
        
        public void Appear() { }
        public void OnLastRewardReachedTarget() { }
    }
}