using _FeatureHub.UI.Runtime;
using TMPro.EditorUtilities;
using UnityEditor;
using UnityEngine;

namespace _FeatureHub.UI.Editor
{
    [CustomEditor(typeof(BaseTMProUGUI<>), true)]
    [CanEditMultipleObjects]
    public class BaseTMProUGUIEditor : TMP_EditorPanelUI
    {
        private GUIStyle m_TitleStyle;
        private SerializedProperty m_KeyProperty;
        
        protected override void OnEnable()
        {
            base.OnEnable();
            m_TitleStyle = new GUIStyle { fontSize = 12, fontStyle = FontStyle.Bold, richText = true, alignment = TextAnchor.MiddleCenter};
            m_KeyProperty = serializedObject.FindProperty("key");
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            EditorGUILayout.Separator();
            GUILayout.Label("<b><color=grey>───────────────【Extender Region】───────────────</color></b>", m_TitleStyle);
            serializedObject.Update();
            EditorGUILayout.PropertyField(m_KeyProperty);
            serializedObject.ApplyModifiedProperties();
        }
    }
}