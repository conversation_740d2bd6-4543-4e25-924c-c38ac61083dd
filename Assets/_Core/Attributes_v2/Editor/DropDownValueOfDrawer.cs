using System;
using _FeatureHub.Attributes.Core;
using UnityEditor;
using UnityEngine;

namespace _FeatureHub.Attributes.Editor
{
    [CustomPropertyDrawer(typeof(DropDownValueOfAttribute))]
    public class DropDownValueOfDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            property.serializedObject.Update();
            var customizedAttribute = (DropDownValueOfAttribute)attribute;
            var index = Array.IndexOf(customizedAttribute.fieldValues, property.stringValue);
            if (index < 0)
            {
                EditorGUI.BeginChangeCheck();
                index = EditorGUI.Popup(position, label.text, customizedAttribute.fieldNameFlags.Length - 1, customizedAttribute.fieldNameFlags, PresetGUIStyle.errorPopup);
                if (EditorGUI.EndChangeCheck())
                {
                    property.stringValue = customizedAttribute.fieldValues[index];
                    property.serializedObject.ApplyModifiedProperties();
                    return;
                }

                drawErrorBox("KeyNotFoundException: Deselect to fix it.");
                return;
            }
            
            if (customizedAttribute.isUniqueID)
            {
                var isDuplicated = false;
                var originalValue = property.stringValue;
                var iterator = property.serializedObject.GetIterator();
                iterator.NextVisible(true);
                while (iterator.NextVisible(true))
                {
                    if (iterator.propertyPath.Equals(property.propertyPath, StringComparison.Ordinal)
                        || iterator.propertyType != property.propertyType)
                        continue;
                    if (iterator.stringValue.Equals(originalValue, StringComparison.Ordinal))
                        isDuplicated = true;
                }
                
                if (isDuplicated)
                    drawErrorBox("Duplicate value detected.");
            }
            
            EditorGUI.BeginChangeCheck();
            index = EditorGUI.Popup( position, label.text, index, customizedAttribute.fieldNames);
            if (EditorGUI.EndChangeCheck())
                property.stringValue = customizedAttribute.fieldValues[index];
            

            
            property.serializedObject.ApplyModifiedProperties();
            
            return;
            
            void drawErrorBox(string errorMessage)
            {
                GUILayout.Space(EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing);
                EditorGUI.HelpBox(new Rect(position)
                    {
                        width = position.width - EditorGUIUtility.labelWidth - 2, x = position.x + EditorGUIUtility.labelWidth + 2,
                        y = position.y + EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing
                    }
                    , errorMessage, MessageType.Error);
            }
        }
    }
}