.bg {
    background-color: rgb(45, 45, 45);
    border-radius: 5px;
}

.bg-color-primary{
    background-color: rgb(45, 45, 45);
}

.container {
    background-color: rgb(45, 45, 45);
    
    border-radius: 5px;
    min-height: 30px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding-bottom: 5px;
}

.container-module {
    background-color: rgb(45, 45, 45);

    border-radius: 5px;
    border-width: 1px;
    border-color: wheat;
    min-height: 30px;
    padding: 10px;
    margin: 5px;
}

.pd-10{
    padding: 10px;
}

.pd-5{
    padding: 5px;
}

.container Label{
    -unity-text-align: middle-center;
    -unity-font-style: bold;
    flex-grow: 1;
}

.container-text {
    background-color: rgb(45, 45, 45);

    border-radius: 5px;
    min-height: 30px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding-bottom: 5px;
}

.center-text {
    -unity-text-align: middle-center;
    -unity-font-style: bold;
    flex-grow: 1;
}

.om-btn{
    background-color: rgb(82, 82, 82);
    border-radius: 0;
    border-width: 0;
}

.om-btn:hover{
    background-color: rgb(239, 100, 34);
}

.om-btn:focus{
}

.anchor-top-left{
    position: absolute;
    top: 10px;
    right: 10px;
}



.warning-text{
    -unity-text-align: middle-center;
    -unity-font-style: bold;
    border-color: rgba(229, 232, 110, 0.82);
    background-color: rgb(45, 45, 45);
    border-radius: 5px;
    border-width: 2px;
    padding: 5px;
}