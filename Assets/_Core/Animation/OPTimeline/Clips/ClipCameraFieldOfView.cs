using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Camera/Field Of View", "Camera Field Of View")]
    public class ClipCameraFieldOfView : OPTweenClip<float,Camera>
    {
        protected override float GetCurrentValue()
        {
            return target.fieldOfView;
        }

        protected override void SetValue(float newValue)
        {
            target.fieldOfView = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}