using UnityEngine;

namespace OnePuz.OPTimeline
{
    public static class OPExtension 
    {
        /// <summary>
        /// Add clip to animator
        /// and invoke OnClipAddedToOPAnimator
        /// and record undo
        /// </summary>
        /// <param name="animator">the target Animator</param>
        /// <param name="clip">the new Clip</param>
        public static void AddClip(this OPAnimator animator, OPClip clip)
        {
#if UNITY_EDITOR
            UnityEditor.Undo.RecordObject(animator, "Add Clip");
#endif
            animator.GetClips().Add(clip);
            clip.OnClipAddedToOPAnimator(animator);
        }
        
        /// <summary>
        /// Remove the clip from the animator
        /// + record undo
        /// </summary>
        /// <param name="animator"></param>
        /// <param name="clip"></param>
        public static void RemoveClip(this OPAnimator animator, OPClip clip)
        {
#if UNITY_EDITOR
            UnityEditor.Undo.RecordObject(animator, "Add Clip");
#endif
            animator.GetClips().Remove(clip);
        }
        
    }
}