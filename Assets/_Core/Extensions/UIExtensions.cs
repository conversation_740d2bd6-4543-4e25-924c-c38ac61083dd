using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace OnePuz.Extensions
{
    public static class UIExtensions
    {
        public static void SetOnClick(this Button button, UnityAction action)
        {
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(action);
        }

        public static void SetOnClick<T>(this Button button, T target, UnityAction<T> action)
        {
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() => action?.Invoke(target));
        }

        public static void SetAlpha(this Graphic graphic, float alpha)
        {
            var color = graphic.color;
            color.a = alpha;
            graphic.color = color;
        }

        public static void SizeDeltaX(this RectTransform rectTransform, float x)
        {
            var sizeDelta = rectTransform.sizeDelta;
            sizeDelta.x = x;
            rectTransform.sizeDelta = sizeDelta;
        }

        public static void SizeDeltaY(this RectTransform rectTransform, float y)
        {
            var sizeDelta = rectTransform.sizeDelta;
            sizeDelta.y = y;
            rectTransform.sizeDelta = sizeDelta;
        }
    }

}