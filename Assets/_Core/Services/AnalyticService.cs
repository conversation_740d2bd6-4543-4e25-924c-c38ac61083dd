using System.Collections.Generic;
using AppsFlyerSDK;
using Firebase.Analytics;

namespace OnePuz.Services
{
    public struct FirebaseCustomEventData
    {
        public string eventName;
        public Parameter[] parameters;
    }
    
    public class AnalyticService : IServiceLoad
    {
        private bool _isFirebaseReadyToUse;
        private readonly List<FirebaseCustomEventData> _fireBaseCustomEventQueue = new ();
        
        public void Load()
        {
            
        }
        
        public void ConfirmFirebaseReadyToUse()
        {
            _isFirebaseReadyToUse = true;
        }

        public void LogEvent(string eventName, Parameter[] parameters = null)
        {
            if (!_isFirebaseReadyToUse)
            {
                _fireBaseCustomEventQueue.Add(new FirebaseCustomEventData() { eventName = eventName, parameters = parameters });
            }
            else
            {
                if (parameters == null)
                    FirebaseAnalytics.LogEvent(eventName);
                else
                    FirebaseAnalytics.LogEvent(eventName, parameters);

                OLogger.Log($"Instantly Firebase Log: {eventName}");
            }
        }

        public void FirebaseLogQueuedEvents()
        {
            OLogger.Log($"Log queued events! Count: {_fireBaseCustomEventQueue.Count}");
            if (_fireBaseCustomEventQueue.Count <= 0) return;
            for (var i = 0; i < _fireBaseCustomEventQueue.Count; i++)
            {
                var data = _fireBaseCustomEventQueue[i];
                if (data.parameters == null)
                    FirebaseAnalytics.LogEvent(data.eventName);
                else
                    FirebaseAnalytics.LogEvent(data.eventName, data.parameters);

                OLogger.Log($"Queued Firebase Log: {data.eventName}");
            }

            _fireBaseCustomEventQueue.Clear();
        }

        public void SetUserProperty(string key, string value)
        {
            OLogger.Log($"Set User Property: {key} - {value}");
            if (_isFirebaseReadyToUse)
                FirebaseAnalytics.SetUserProperty(key, value);
        }
    }
}