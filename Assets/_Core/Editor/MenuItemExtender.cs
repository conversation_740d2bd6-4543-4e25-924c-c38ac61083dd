using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace _FeatureHub.Editor
{
    public static class MenuItemExtender
    {
        private const string MenuSeparator = "GameObject/👀 Extender/Create Separator &-";
        private const string MenuSeparatorCopy = "GameObject/👀 Extender/Copy Separator &#-";
        private const string MenuAssignedPath = "GameObject/👀 Extender/Copy Assigned Path &;";
        private const string MenuAssignedPathForSelectedObjects = "GameObject/👀 Extender/Copy Assigned Paths For Selected Objects &#;";
        
        [MenuItem(MenuSeparator, priority = -2)]
        public static void CreateSeparator()
        {
            var obj = new GameObject("──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────");
            obj.transform.SetParent(Selection.activeTransform);
            obj.SetActive(false);
            Undo.RegisterCreatedObjectUndo(obj, "Create Separator");
        }
        
        [MenuItem(MenuSeparatorCopy, priority = -2)]
        public static void CopySeparator()
        {
            GUIUtility.systemCopyBuffer = "──────────";
        }

        [MenuItem(MenuAssignedPath, priority = -1)]
        public static void LogAssignedPath()
        {
            var builder = new StringBuilder();
            builder.Append("public const string ");
            builder.Append(Regex.Replace(Selection.activeTransform.name, "[^a-zA-Z0-9_]", ""));
            builder.Append(" = ");
            builder.Append("\"");
            builder.Append(Helper.GetPathFinder(Selection.activeTransform));
            builder.AppendLine("\";");
            GUIUtility.systemCopyBuffer = Helper.GetPathFinder(Selection.activeTransform);
        }

        [MenuItem(MenuAssignedPath, true)]
        public static bool Validate_LogAssignedPath() => Selection.activeTransform != null && Selection.activeTransform.parent != null;

        [MenuItem(MenuAssignedPathForSelectedObjects, priority = -1)]
        private static void LogAssignedPathsForSelectedObjects()
        {
            var builder = new StringBuilder();
            var selectedObjects = Selection.GetTransforms(SelectionMode.Unfiltered);

            foreach (var entry in selectedObjects)
            {
                builder.Append("public const string ");
                builder.Append(Regex.Replace(entry.name, "[^a-zA-Z0-9_]", ""));
                builder.Append(" = ");
                builder.Append("\"");
                builder.Append(Helper.GetPathFinder(entry));
                builder.AppendLine("\";");
            }

            GUIUtility.systemCopyBuffer = builder.ToString();
        }

        [MenuItem(MenuAssignedPathForSelectedObjects, true)]
        private static bool Validate_LogAssignedPathsForSelectedObjects()
        {
            var selectedObjects = Selection.GetTransforms(SelectionMode.Unfiltered);
            return selectedObjects.Length > 0 && selectedObjects.All(entry => entry.parent != null);
        }

        private static class Helper
        {
            public static string GetPathFinder(Transform source)
            {
                var root = source.root;

                if (root.hideFlags == (HideFlags.NotEditable | HideFlags.DontSave))
                    root = root.GetChild(0);
                
                var builder = new StringBuilder();
                var temp = source;
            
                builder.Insert(0, $"/{source.name}");
            
                for (var i = 0; i < 99; i++)
                {
                    temp = temp.parent;
                    if (string.CompareOrdinal(temp.name, root.name) != 0)
                    {
                        builder.Insert(0, $"/{temp.name}");
                    }
                    else
                    {
                        builder.Remove(0, 1);
                        break;
                    }
                }
                return builder.ToString();
            }
        }
    }
}