
namespace OnePuz.Services.Internal
{
	public static class ServiceCache
	{
		private static IEventBusService eventBusService = null;
		private static ITaskService taskService = null;
		private static ICoroutineService coroutineService = null;
		private static ITimeService timeService = null;

		public static IEventBusService EventBus
		{
			get
			{
				if (eventBusService == null)
				{
					eventBusService = ServiceContainer.Get<IEventBusService>();
				}

				return eventBusService;
			}
		}

		public static ITaskService Task
		{
			get
			{
				if (taskService == null)
				{
					taskService = ServiceContainer.Get<ITaskService>();
				}

				return taskService;
			}
		}

		public static ICoroutineService Coroutine
		{
			get
			{
				if (coroutineService == null)
				{
					coroutineService = ServiceContainer.Get<ICoroutineService>();
				}

				return coroutineService;
			}
		}

		public static ITimeService Time
		{
			get
			{
				if (timeService == null)
				{
					timeService = ServiceContainer.Get<ITimeService>();
				}

				return timeService;
			}
		}

		internal static void Dispose()
		{
			eventBusService = null;
			taskService = null;
			coroutineService = null;
			timeService = null;
		}
	}
}
