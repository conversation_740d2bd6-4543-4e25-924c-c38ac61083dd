// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using System;
using System.Collections.Generic;
using System.Linq;

namespace OnePuz.Extensions
{
	public static class ICollectionExtensions
	{
		public static void RemoveIf<TSource>(this ICollection<TSource> self, Func<TSource, bool> condition)
		{
			var reversed = self.Reverse();

			using var enumerator = reversed.GetEnumerator();
			while (enumerator.MoveNext())
			{
				if (condition(enumerator.Current))
				{
					self.Remove(enumerator.Current);
				}
			}
		}
	}
}
