using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.Utilities
{
    [ExecuteInEditMode]
    public class CurvedGridLayout : MonoBehaviour
    {
        public enum AlignmentOption
        {
            Left,
            Center,
            Right
        }

        [Header("Child Settings")]
        [SerializeField] private Vector2 _childSize = new Vector2(100f, 100f);

        [Header("Grid Settings")]
        [SerializeField] private int _rows = 1;
        [SerializeField] private int _columns = 1;
        [SerializeField] private Vector2 _spacing = new Vector2(10f, 10f);
        [SerializeField] private AlignmentOption _alignment = AlignmentOption.Center;
        
        [Header("Curve Settings")]
        [SerializeField] private float _curveFactor = 0f;
        [SerializeField] private Vector3 _curveDirection = Vector3.up;
        
        [Header("Options")]
        [SerializeField] private bool _forceChildPositions = true;
        [SerializeField] private bool _updateContinuously = false;
        [SerializeField] private bool _showGizmos = true;

        private readonly List<RectTransform> _rectChildren = new List<RectTransform>();
        private readonly List<Transform> _children = new List<Transform>();
        private Vector2 _actualContainerSize;
        private bool _useUICoordinates = false;

        #if UNITY_EDITOR
        private void OnEnable()
        {
            _useUICoordinates = GetComponent<RectTransform>() != null;
            ArrangeChildren();
        }
        #endif
        
        public void Setup(int rows, int columns, bool updateContinuously = false)
        {
            _rows = rows;
            _columns = columns;
            _updateContinuously = updateContinuously;
            ArrangeChildren();
        }

        private void Update()
        {
            if (_updateContinuously)
                ArrangeChildren();
        }

        private void OnTransformChildrenChanged() => ArrangeChildren();

        public void ArrangeChildren()
        {
            if (_useUICoordinates)
            {
                CollectRectChildren();
                ArrangeRectChildren();
            }
            else
            {
                CollectChildren();
                ArrangeTransformChildren();
            }
        }

        private void CollectRectChildren()
        {
            _rectChildren.Clear();
            for (var i = 0; i < transform.childCount; i++)
            {
                if (transform.GetChild(i) is RectTransform rect && rect.gameObject.activeSelf)
                    _rectChildren.Add(rect);
            }
        }

        private void CollectChildren()
        {
            _children.Clear();
            for (int i = 0; i < transform.childCount; i++)
            {
                var child = transform.GetChild(i);
                if (child.gameObject.activeSelf)
                    _children.Add(child);
            }
        }

        private Vector2 GetContainerSize()
        {
            return _childSize * new Vector2(_columns, _rows) + new Vector2(_spacing.x * (_columns - 1), _spacing.y * (_rows - 1));
        }

        private void ArrangeRectChildren()
        {
            if (_rectChildren.Count == 0) return;
            
            // Get layout parameters
            var layoutParams = CalculateLayoutParameters(_rectChildren.Count);
            
            for (int i = 0; i < _rectChildren.Count; i++)
            {
                var (row, col) = GetRowAndColumn(i, layoutParams.columns);
                var itemsInThisRow = GetItemsInRow(row, layoutParams.actualRows, _rectChildren.Count, layoutParams.columns);
                
                // Calculate row width and alignment offset
                var rowWidth = CalculateRowWidth(itemsInThisRow, layoutParams.cellWidth, _spacing.x);
                var rowAlignmentOffsetX = CalculateRowAlignmentOffset(_alignment, rowWidth, layoutParams.containerWidth);
                
                // Calculate base position (centered in cell)
                var x = rowAlignmentOffsetX + (col * (layoutParams.cellWidth + _spacing.x)) + layoutParams.cellWidth * 0.5f - layoutParams.containerWidth * 0.5f;
                var y = -row * (layoutParams.cellHeight + _spacing.y) - layoutParams.cellHeight * 0.5f + layoutParams.containerHeight * 0.5f;

                // Apply curve
                var normalizedX = itemsInThisRow > 1 ? (float)col / (itemsInThisRow - 1) : 0.5f;
                var curveOffset = CalculateCurveOffset(normalizedX, itemsInThisRow);

                // Set position
                if (_forceChildPositions)
                    _rectChildren[i].anchoredPosition = new Vector2(x, y) + curveOffset;
            }
        }

        private void ArrangeTransformChildren()
        {
            if (_children.Count == 0) return;
            
            // Get layout parameters
            var layoutParams = CalculateLayoutParameters(_children.Count);
            
            for (var i = 0; i < _children.Count; i++)
            {
                var (row, col) = GetRowAndColumn(i, layoutParams.columns);
                var itemsInThisRow = GetItemsInRow(row, layoutParams.actualRows, _children.Count, layoutParams.columns);
                
                // Calculate row width and alignment offset
                var rowWidth = CalculateRowWidth(itemsInThisRow, layoutParams.cellWidth, _spacing.x);
                var rowAlignmentOffsetX = CalculateWorldSpaceRowAlignmentOffset(_alignment, rowWidth, layoutParams.containerWidth);
                
                // Calculate base position
                var x = rowAlignmentOffsetX + col * (layoutParams.cellWidth + _spacing.x) + layoutParams.cellWidth * 0.5f;
                var y = layoutParams.containerHeight * 0.5f - row * (layoutParams.cellHeight + _spacing.y) - layoutParams.cellHeight * 0.5f;

                // Apply curve
                var normalizedX = itemsInThisRow > 1 ? (float)col / (itemsInThisRow - 1) : 0.5f;
                var curveOffset = CalculateCurveOffset(normalizedX, itemsInThisRow);

                // Set position
                if (_forceChildPositions)
                    _children[i].localPosition = new Vector2(x, y) + curveOffset;
            }
        }

        private (int row, int col) GetRowAndColumn(int index, int columns)
        {
            return (index / columns, index % columns);
        }

        private int GetItemsInRow(int row, int actualRows, int totalItems, int columns)
        {
            return (row == actualRows - 1) ? totalItems - (row * columns) : columns;
        }

        private float CalculateRowWidth(int itemsInRow, float cellWidth, float spacingX)
        {
            var rowWidth = cellWidth * itemsInRow;
            if (itemsInRow > 1)
                rowWidth += spacingX * (itemsInRow - 1);
            return rowWidth;
        }

        private float CalculateRowAlignmentOffset(AlignmentOption alignment, float rowWidth, float containerWidth)
        {
            return alignment switch
            {
                AlignmentOption.Left => 0,
                AlignmentOption.Center => (containerWidth - rowWidth) * 0.5f,
                AlignmentOption.Right => containerWidth - rowWidth,
                _ => 0
            };
        }

        private float CalculateWorldSpaceRowAlignmentOffset(AlignmentOption alignment, float rowWidth, float containerWidth)
        {
            return alignment switch
            {
                AlignmentOption.Left => -containerWidth * 0.5f,
                AlignmentOption.Center => -rowWidth * 0.5f,
                AlignmentOption.Right => containerWidth * 0.5f - rowWidth,
                _ => 0
            };
        }

        private Vector2 CalculateCurveOffset(float normalizedX, int itemsInRow)
        {
            var curveStrength = 0f;
            
            if (itemsInRow > 1)
                curveStrength = Mathf.Sin(normalizedX * Mathf.PI) * _curveFactor;
            else
                curveStrength = _curveFactor;

            return new Vector2(
                _curveDirection.x * curveStrength,
                _curveDirection.y * curveStrength
            );
        }

        private (int columns, int actualRows, float containerWidth, float containerHeight, float cellWidth, float cellHeight) 
        CalculateLayoutParameters(int itemCount)
        {
            // Ensure valid rows and columns
            var columns = Mathf.Max(1, _columns);
            var rows = Mathf.Max(1, _rows);
            
            // Calculate actual rows needed
            var actualRows = Mathf.CeilToInt((float)itemCount / columns);
            
            // Get container size
            _actualContainerSize = GetContainerSize();
            var containerWidth = _actualContainerSize.x;
            var containerHeight = _actualContainerSize.y;
            
            // Use child size directly
            var cellWidth = _childSize.x;
            var cellHeight = _childSize.y;
            
            return (columns, actualRows, containerWidth, containerHeight, cellWidth, cellHeight);
        }

        // Draw gizmos to visualize the layout
        private void OnDrawGizmos()
        {
            if (!_showGizmos) return;
            
            // Update container size for gizmos
            _actualContainerSize = GetContainerSize();
            
            // Draw container bounds
            Gizmos.color = new Color(0.2f, 0.2f, 0.8f, 0.5f);
            var center = transform.position;
            var size = new Vector3(_actualContainerSize.x, _actualContainerSize.y, 0.01f);
            
            Gizmos.matrix = Matrix4x4.TRS(center, transform.rotation, Vector3.one);
            Gizmos.DrawCube(Vector3.zero, size);
            
            // Reset gizmos matrix
            Gizmos.matrix = Matrix4x4.identity;
        }
        
        [Button("Update Layout")]
        public void UpdateLayout() => ArrangeChildren();

        public Vector2 GetPositionForIndex(int index)
        {
            // Calculate layout parameters
            var layoutParams = CalculateLayoutParameters(_useUICoordinates ? _rectChildren.Count : _children.Count);
            
            var (row, col) = GetRowAndColumn(index, layoutParams.columns);
            var itemsInThisRow = GetItemsInRow(row, layoutParams.actualRows, 
                _useUICoordinates ? _rectChildren.Count : _children.Count, layoutParams.columns);
            
            // Calculate row width and alignment offset
            var rowWidth = CalculateRowWidth(itemsInThisRow, layoutParams.cellWidth, _spacing.x);
            
            Vector2 position;
            
            if (_useUICoordinates)
            {
                var rowAlignmentOffsetX = CalculateRowAlignmentOffset(_alignment, rowWidth, layoutParams.containerWidth);
                
                // Calculate base position (centered in cell)
                var x = rowAlignmentOffsetX + (col * (layoutParams.cellWidth + _spacing.x)) + layoutParams.cellWidth * 0.5f - layoutParams.containerWidth * 0.5f;
                var y = -row * (layoutParams.cellHeight + _spacing.y) - layoutParams.cellHeight * 0.5f + layoutParams.containerHeight * 0.5f;
                
                position = new Vector2(x, y);
            }
            else
            {
                var rowAlignmentOffsetX = CalculateWorldSpaceRowAlignmentOffset(_alignment, rowWidth, layoutParams.containerWidth);
                
                // Calculate base position
                var x = rowAlignmentOffsetX + col * (layoutParams.cellWidth + _spacing.x) + layoutParams.cellWidth * 0.5f;
                var y = layoutParams.containerHeight * 0.5f - row * (layoutParams.cellHeight + _spacing.y) - layoutParams.cellHeight * 0.5f;
                
                position = new Vector2(x, y);
            }
            
            // Apply curve
            var normalizedX = itemsInThisRow > 1 ? (float)col / (itemsInThisRow - 1) : 0.5f;
            var curveOffset = CalculateCurveOffset(normalizedX, itemsInThisRow);
            
            return position + curveOffset;
        }
    }
}