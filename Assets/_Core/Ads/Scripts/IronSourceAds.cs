#if USING_IRONSOURCE
using System;
using System.Collections.Generic;
using AppsFlyerSDK;
using OnePuz.Services;

#if USING_APPSFLYER
using AppsFlyerSDK;
#endif
using UnityEngine;

namespace OnePuz.Ads
{
    public class IronSourceAds : IAdController
    {
        #region Properties
        private bool _isBannerLoaded;
        private bool _hasRewarded;

        private BaseAdUnitDefinition _definitions;

        private Action<bool> _onBannerLoaded;
        private Action<bool> _onInterstitialLoaded;
        private Action<bool> _onVideoRewardedLoaded;

        private IAdService _manager;

        public bool IsInitialized { get; set; } = false;
        #endregion

        #region Unity Events
        public void OnApplicationPause(bool isPaused)
        {
            IronSource.Agent.onApplicationPause(isPaused);
        }
        #endregion

        #region Methods
        public void Init(IAdService manager, BaseAdUnitDefinition definitions)
        {
            _manager = manager;
            _definitions = definitions;

            OLogger.Log($"[{_definitions.ProviderToString}] Is Initializing!");

            IronSource.Agent.setConsent(true);
            IronSource.Agent.setMetaData("do_not_sell","false");
            IronSource.Agent.setMetaData("is_child_directed","false");
            // IronSource.Agent.setMetaData("is_test_suite", "enable"); 

            IronSource.Agent.validateIntegration();
            IronSource.Agent.init(_definitions.GameId);
            IronSourceAdQuality.Initialize(_definitions.GameId);

            IronSourceEvents.onSdkInitializationCompletedEvent += SdkInitializationCompletedEvent;

            // IronSource.Agent.setAdaptersDebug(true);

            _isBannerLoaded = false;
            _hasRewarded = false;

            //Add Rewarded Video Events
            IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoAdOpenedEvent;
            IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoAdClosedEvent;
            IronSourceRewardedVideoEvents.onAdAvailableEvent += RewardedVideoAvailableEvent;
            IronSourceRewardedVideoEvents.onAdUnavailableEvent += RewardedVideoUnavailableEvent;
            IronSourceRewardedVideoEvents.onAdOpenedEvent += RewardedVideoAdStartedEvent;
            IronSourceRewardedVideoEvents.onAdClosedEvent += RewardedVideoAdEndedEvent;
            IronSourceRewardedVideoEvents.onAdRewardedEvent += RewardedVideoAdRewardedEvent;
            IronSourceRewardedVideoEvents.onAdShowFailedEvent += RewardedVideoAdShowFailedEvent;
            IronSourceRewardedVideoEvents.onAdClickedEvent += RewardedVideoAdClickedEvent;

            // Add Interstitial Events
            IronSourceInterstitialEvents.onAdReadyEvent += InterstitialAdReadyEvent;
            IronSourceInterstitialEvents.onAdLoadFailedEvent += InterstitialAdLoadFailedEvent;
            IronSourceInterstitialEvents.onAdShowSucceededEvent += InterstitialAdShowSucceededEvent;
            IronSourceInterstitialEvents.onAdShowFailedEvent += InterstitialAdShowFailedEvent;
            IronSourceInterstitialEvents.onAdClickedEvent += InterstitialAdClickedEvent;
            IronSourceInterstitialEvents.onAdOpenedEvent += InterstitialAdOpenedEvent;
            IronSourceInterstitialEvents.onAdClosedEvent += InterstitialAdClosedEvent;

            // Add Banner Events
            IronSourceBannerEvents.onAdLoadedEvent += BannerAdLoadedEvent;
            IronSourceBannerEvents.onAdLoadFailedEvent += BannerAdLoadFailedEvent;
            IronSourceBannerEvents.onAdClickedEvent += BannerAdClickedEvent;
            IronSourceBannerEvents.onAdScreenPresentedEvent += BannerAdScreenPresentedEvent;
            IronSourceBannerEvents.onAdScreenDismissedEvent += BannerAdScreenDismissedEvent;
            IronSourceBannerEvents.onAdLeftApplicationEvent += BannerAdLeftApplicationEvent;

            IronSourceEvents.onImpressionDataReadyEvent += ImpressionDataReadyEvent;
        }

        private void SdkInitializationCompletedEvent()
        {
            IsInitialized = true;

            // IronSource.Agent.launchTestSuite();
        }

        public void RequestBanner(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting banner!");
            _onBannerLoaded = onLoaded;

            if (IsBannerLoaded() && _onBannerLoaded != null)
            {
                _onBannerLoaded(true);
            }
            else
            {
                IronSourceBannerSize ironSourceBannerSize = IronSourceBannerSize.BANNER;
                float width = IronSource.Agent.getDeviceScreenWidth();
                float height = IronSource.Agent.getMaximalAdaptiveHeight(width);
                ISContainerParams isContainerParams = new ISContainerParams { Width = width, Height = height };
                ironSourceBannerSize.setBannerContainerParams(isContainerParams);
                ironSourceBannerSize.SetAdaptive(true);
                IronSource.Agent.loadBanner(ironSourceBannerSize, IronSourceBannerPosition.BOTTOM);
            }
        }

        public void ShowBanner()
        {
            IronSource.Agent.displayBanner();
        }

        public void HideBanner()
        {
            IronSource.Agent.hideBanner();
        }

        public void DestroyBanner()
        {
            IronSource.Agent.destroyBanner();
            _isBannerLoaded = false;
        }

        public bool IsBannerLoaded()
        {
            return _isBannerLoaded;
        }

        public void StartBannerAutoRefresh()
        {
        }

        public void StopBannerAutoRefresh()
        {
        }

        public void RequestInterstitial(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting interstitial!");
            _onInterstitialLoaded = onLoaded;

            if (IsInterstitialLoaded() && _onInterstitialLoaded != null)
            {
                _onInterstitialLoaded(true);
            }
            else
            {
                IronSource.Agent.loadInterstitial();
            }
        }

        public void ShowInterstitial()
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Show Interstitial!");
            IronSource.Agent.showInterstitial();
        }

        public bool IsInterstitialLoaded()
        {
            return IronSource.Agent.isInterstitialReady();
        }

        public void RequestRewardedVideo(Action<bool> onLoaded)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] requesting rewarded video!");
            _onVideoRewardedLoaded = onLoaded;

            if (_onVideoRewardedLoaded != null)
            {
                _onVideoRewardedLoaded(IsRewardedVideoLoaded());
            }
        }

        public bool IsRewardedVideoLoaded()
        {
            return IronSource.Agent.isRewardedVideoAvailable();
        }

        public void ShowRewardedVideo()
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Show Rewarded Video!");
            _hasRewarded = false;
            IronSource.Agent.showRewardedVideo();
        }

        public void Dispose()
        {
            _onBannerLoaded = null;
            _onInterstitialLoaded = null;
            _onVideoRewardedLoaded = null;

            IronSourceEvents.onSdkInitializationCompletedEvent -= SdkInitializationCompletedEvent;

            IronSourceRewardedVideoEvents.onAdOpenedEvent -= RewardedVideoAdOpenedEvent;
            IronSourceRewardedVideoEvents.onAdClosedEvent -= RewardedVideoAdClosedEvent;
            IronSourceRewardedVideoEvents.onAdAvailableEvent -= RewardedVideoAvailableEvent;
            IronSourceRewardedVideoEvents.onAdUnavailableEvent -= RewardedVideoUnavailableEvent;
            IronSourceRewardedVideoEvents.onAdOpenedEvent -= RewardedVideoAdStartedEvent;
            IronSourceRewardedVideoEvents.onAdClosedEvent -= RewardedVideoAdEndedEvent;
            IronSourceRewardedVideoEvents.onAdRewardedEvent -= RewardedVideoAdRewardedEvent;
            IronSourceRewardedVideoEvents.onAdShowFailedEvent -= RewardedVideoAdShowFailedEvent;
            IronSourceRewardedVideoEvents.onAdClickedEvent -= RewardedVideoAdClickedEvent;

            IronSourceInterstitialEvents.onAdReadyEvent -= InterstitialAdReadyEvent;
            IronSourceInterstitialEvents.onAdLoadFailedEvent -= InterstitialAdLoadFailedEvent;
            IronSourceInterstitialEvents.onAdShowSucceededEvent -= InterstitialAdShowSucceededEvent;
            IronSourceInterstitialEvents.onAdShowFailedEvent -= InterstitialAdShowFailedEvent;
            IronSourceInterstitialEvents.onAdClickedEvent -= InterstitialAdClickedEvent;
            IronSourceInterstitialEvents.onAdOpenedEvent -= InterstitialAdOpenedEvent;
            IronSourceInterstitialEvents.onAdClosedEvent -= InterstitialAdClosedEvent;

            IronSourceBannerEvents.onAdLoadedEvent -= BannerAdLoadedEvent;
            IronSourceBannerEvents.onAdLoadFailedEvent -= BannerAdLoadFailedEvent;
            IronSourceBannerEvents.onAdClickedEvent -= BannerAdClickedEvent;
            IronSourceBannerEvents.onAdScreenPresentedEvent -= BannerAdScreenPresentedEvent;
            IronSourceBannerEvents.onAdScreenDismissedEvent -= BannerAdScreenDismissedEvent;
            IronSourceBannerEvents.onAdLeftApplicationEvent -= BannerAdLeftApplicationEvent;

            IronSourceEvents.onImpressionDataReadyEvent -= ImpressionDataReadyEvent;
        }

        private void ImpressionDataReadyEvent(IronSourceImpressionData impressionData)
        {
            OLogger.Log($"[IronSource] ImpressionDataReadyEvent, data null? {(impressionData == null)}");
            if (impressionData != null)
            {
                Firebase.Analytics.Parameter[] AdParameters = {
                    new Firebase.Analytics.Parameter("ad_platform", "ironSource"),
                    new Firebase.Analytics.Parameter("ad_source", impressionData.adNetwork),
                    new Firebase.Analytics.Parameter("ad_unit_name", impressionData.instanceName),
                    new Firebase.Analytics.Parameter("ad_format", impressionData.adUnit),
                    new Firebase.Analytics.Parameter("currency","USD"),
                    new Firebase.Analytics.Parameter("value", impressionData.revenue.GetValueOrDefault())
                };
                Core.Analytic.LogEvent("ad_impression", AdParameters);

                OLogger.Log($"[IronSource] ImpressionDataReadyEvent, value {impressionData.revenue.GetValueOrDefault()}");
                
                Dictionary<string, string> additionalParams = new Dictionary<string, string>
                {
                    { AFAdRevenueEvent.AD_UNIT, impressionData.instanceName },
                    { AFAdRevenueEvent.AD_TYPE, impressionData.adUnit },
                    { AFAdRevenueEvent.PLACEMENT, impressionData.placement },
                    { "af_quantity", "1" }
                };

                AppsFlyerAdRevenue.logAdRevenue("IronSource", AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeIronSource, impressionData.revenue.GetValueOrDefault(), "USD", additionalParams);
            }
        }

        #region RewardedAd callback handlers
        private void RewardedVideoAvailableEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAvailableEvent, value = " + adInfo.revenue);
            _onVideoRewardedLoaded?.Invoke(true);

            _manager.HandleRewardedVideoLoaded();
        }

        private void RewardedVideoUnavailableEvent()
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoUnavailableEvent");
            _onVideoRewardedLoaded?.Invoke(false);
        }

        private void RewardedVideoAdOpenedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdOpenedEvent");

            _manager.HandleRewardedVideoDisplayed();
        }

        private void RewardedVideoAdRewardedEvent(IronSourcePlacement ssp, IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdRewardedEvent, amount = " + ssp.getRewardAmount() + " name = " + ssp.getRewardName());
            _hasRewarded = true;
        }

        private void RewardedVideoAdClosedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdClosedEvent");
            _manager.HandleOnWatchVideoReward(_hasRewarded);
        }

        private void RewardedVideoAdStartedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdStartedEvent");
            _hasRewarded = false;
        }

        private void RewardedVideoAdEndedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdEndedEvent");
        }

        private void RewardedVideoAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdShowFailedEvent, code :  " + error.getCode() + ", description : " + error.getDescription());
            _hasRewarded = false;
        }

        private void RewardedVideoAdClickedEvent(IronSourcePlacement ssp, IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got RewardedVideoAdClickedEvent, name = " + ssp.getRewardName());
            _manager.HandleOnAdsClicked();
        }
        #endregion

        #region Interstitial callback handlers
        private void InterstitialAdReadyEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdReadyEvent");
            if (_onInterstitialLoaded != null)
                _onInterstitialLoaded(true);

            _manager.HandleInterstitialLoaded();
        }

        private void InterstitialAdLoadFailedEvent(IronSourceError error)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdLoadFailedEvent, code: " + error.getCode() + ", description : " + error.getDescription());
            if (_onInterstitialLoaded != null)
                _onInterstitialLoaded(false);
        }

        private void InterstitialAdShowSucceededEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdShowSucceededEvent");
            _manager.HandleInterstitialDisplayed();
        }

        private void InterstitialAdShowFailedEvent(IronSourceError error, IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdShowFailedEvent, code :  " + error.getCode() + ", description : " + error.getDescription());
            _manager.HandleOnShowInterstitialFailed();
        }

        private void InterstitialAdClickedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdClickedEvent");
            _manager.HandleOnAdsClicked();
        }

        private void InterstitialAdOpenedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdOpenedEvent");
        }

        private void InterstitialAdClosedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got InterstitialAdClosedEvent");
            _manager.HandleOnClosedInterstitial();
        }
        #endregion

        #region Banner callback handlers
        private void BannerAdLoadedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner loaded!");
            if (_onBannerLoaded != null)
                _onBannerLoaded(true);

            _isBannerLoaded = true;

            _manager.HandleBannerLoaded(true);
        }

        private void BannerAdLoadFailedEvent(IronSourceError error)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner failed to load with code: {error.getCode()}, description: {error.getDescription()}");
            _onBannerLoaded?.Invoke(false);

            _isBannerLoaded = false;

            _manager.HandleBannerLoaded(false);
        }

        private void BannerAdClickedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] Banner clicked");
            _manager.HandleOnAdsClicked();
        }

        private void BannerAdScreenPresentedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got BannerAdScreenPresentedEvent");
        }

        private void BannerAdScreenDismissedEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got BannerAdScreenDismissedEvent");
        }

        private void BannerAdLeftApplicationEvent(IronSourceAdInfo adInfo)
        {
            OLogger.Log($"[{_definitions.ProviderToString}] I got BannerAdLeftApplicationEvent");
            _manager.HandleOnAdsClicked();
        }
        #endregion

        #endregion

        public void RequestAppOpenAd(Action<bool> onLoaded)
        {
            throw new NotImplementedException();
        }

        public bool IsAppOpenAdLoaded()
        {
            throw new NotImplementedException();
        }

        public void ShowAppOpenAd()
        {
            throw new NotImplementedException();
        }
    }
}
#endif